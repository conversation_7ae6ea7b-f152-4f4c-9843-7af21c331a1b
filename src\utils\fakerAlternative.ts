// Faker API 备选方案 - 本地数据生成器
import { BillToInfo } from '@/types/invoice';
import { getLocaleInfo } from './localeConfig';

// 完整的姓名数据库 - 支持所有地区
const NAME_DATABASE: Record<string, { firstNames: string[], lastNames: string[] }> = {
  // === 亚洲 ===
  // 中文姓名
  'zh_CN': {
    firstNames: ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '娟', '涛', '明', '超', '秀英', '霞', '平'],
    lastNames: ['王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗']
  },
  'zh_TW': {
    firstNames: ['志明', '春嬌', '美麗', '建國', '淑芬', '雅婷', '俊傑', '怡君', '宗翰', '佩君', '家豪', '雅雯', '承翰', '怡萱', '宗憲', '佳穎'],
    lastNames: ['陳', '林', '黃', '張', '李', '王', '吳', '劉', '蔡', '楊', '許', '鄭', '謝', '洪', '郭', '邱', '曾', '廖', '賴', '徐']
  },
  
  // 日文姓名
  'ja_JP': {
    firstNames: ['太郎', '花子', '一郎', '美咲', '翔太', '結衣', '大輔', '愛', '健太', '美穂', '拓也', '麻衣', '雄太', '恵', '慎一', '由美'],
    lastNames: ['田中', '佐藤', '鈴木', '高橋', '渡辺', '伊藤', '山本', '中村', '小林', '加藤', '吉田', '山田', '佐々木', '山口', '松本', '井上']
  },
  
  // 韩文姓名
  'ko_KR': {
    firstNames: ['민수', '지영', '현우', '수진', '준호', '미영', '성민', '혜진', '동현', '은지', '재현', '소영', '우진', '예은', '태현', '다은'],
    lastNames: ['김', '이', '박', '최', '정', '강', '조', '윤', '장', '임', '한', '오', '서', '신', '권', '황', '안', '송', '류', '전']
  },
  
  // 东南亚英文姓名
  'en_HK': {
    firstNames: ['Wing', 'Mei', 'Ka', 'Wai', 'Ming', 'Ling', 'Chun', 'Yuen', 'Ho', 'Fai', 'Siu', 'Kit', 'Man', 'Yan', 'Lok', 'Sum'],
    lastNames: ['Chan', 'Wong', 'Lee', 'Lau', 'Ng', 'Cheung', 'Tang', 'Leung', 'Lam', 'Tsang', 'Ho', 'Yu', 'Lai', 'Fong', 'Mak', 'Tse']
  },
  'en_SG': {
    firstNames: ['Wei Ming', 'Li Hua', 'Raj', 'Priya', 'Ahmad', 'Siti', 'David', 'Sarah', 'Kumar', 'Meera', 'Hassan', 'Fatima', 'James', 'Michelle', 'Arjun', 'Kavitha'],
    lastNames: ['Tan', 'Lim', 'Lee', 'Ng', 'Ong', 'Teo', 'Goh', 'Chua', 'Koh', 'Sim', 'Low', 'Yeo', 'Chong', 'Ho', 'Seah', 'Wee']
  },
  'ms_MY': {
    firstNames: ['Ahmad', 'Siti', 'Muhammad', 'Nur', 'Ali', 'Fatimah', 'Hassan', 'Aminah', 'Ibrahim', 'Khadijah', 'Omar', 'Zainab', 'Ismail', 'Maryam', 'Yusof', 'Aishah'],
    lastNames: ['Abdullah', 'Rahman', 'Ahmad', 'Ali', 'Hassan', 'Ibrahim', 'Ismail', 'Omar', 'Yusof', 'Mohamed', 'Mahmud', 'Hamid', 'Rashid', 'Karim', 'Salleh', 'Mansor']
  },
  'th_TH': {
    firstNames: ['สมชาย', 'สมหญิง', 'วิชัย', 'วันทนา', 'สุรชัย', 'สุมาลี', 'ประยุทธ', 'ประภา', 'วิทยา', 'วิไล', 'สมศักดิ์', 'สมใจ', 'ชาญ', 'ชนิดา', 'ธนา', 'ธิดา'],
    lastNames: ['จันทร์', 'แสง', 'ทอง', 'เงิน', 'แก้ว', 'ใส', 'สว่าง', 'ดี', 'งาม', 'สุข', 'รุ่ง', 'เจริญ', 'มั่น', 'คง', 'ยั่ง', 'ยืน']
  },
  'vi_VN': {
    firstNames: ['Minh', 'Linh', 'Hùng', 'Hương', 'Tuấn', 'Thảo', 'Dũng', 'Dung', 'Hải', 'Hạnh', 'Long', 'Lan', 'Nam', 'Nga', 'Phong', 'Phương'],
    lastNames: ['Nguyễn', 'Trần', 'Lê', 'Phạm', 'Hoàng', 'Huỳnh', 'Phan', 'Vũ', 'Võ', 'Đặng', 'Bùi', 'Đỗ', 'Hồ', 'Ngô', 'Dương', 'Lý']
  },
  
  'en_PH': {
    firstNames: ['Jose', 'Maria', 'Juan', 'Ana', 'Antonio', 'Rosa', 'Francisco', 'Carmen', 'Manuel', 'Josefa', 'Pedro', 'Luz', 'Jesus', 'Esperanza', 'Luis', 'Remedios'],
    lastNames: ['Santos', 'Reyes', 'Cruz', 'Bautista', 'Ocampo', 'Garcia', 'Mendoza', 'Torres', 'Tomas', 'Andres', 'Marquez', 'Castillo', 'Iglesias', 'Villanueva', 'Delos Santos', 'Fernandez']
  },
  'id_ID': {
    firstNames: ['Budi', 'Sari', 'Ahmad', 'Siti', 'Andi', 'Dewi', 'Agus', 'Rina', 'Hendra', 'Maya', 'Rudi', 'Indira', 'Dedi', 'Lestari', 'Bambang', 'Wati'],
    lastNames: ['Santoso', 'Wijaya', 'Kurniawan', 'Sari', 'Pratama', 'Utomo', 'Wibowo', 'Susanto', 'Lestari', 'Handoko', 'Setiawan', 'Rahayu', 'Hidayat', 'Suharto', 'Gunawan', 'Permana']
  },
  'en_IN': {
    firstNames: ['Raj', 'Priya', 'Amit', 'Sunita', 'Suresh', 'Kavita', 'Ravi', 'Meera', 'Anil', 'Geeta', 'Vijay', 'Sita', 'Ramesh', 'Lata', 'Ashok', 'Usha'],
    lastNames: ['Sharma', 'Gupta', 'Singh', 'Kumar', 'Verma', 'Agarwal', 'Jain', 'Bansal', 'Sinha', 'Mishra', 'Pandey', 'Tiwari', 'Yadav', 'Saxena', 'Arora', 'Malhotra']
  },
  'bn_BD': {
    firstNames: ['রহিম', 'ফাতেমা', 'করিম', 'খাদিজা', 'আলী', 'আয়েশা', 'হাসান', 'জয়নব', 'হোসেন', 'রোকেয়া', 'আহমেদ', 'সালমা', 'ইব্রাহিম', 'নাসরিন', 'ওমর', 'রাবেয়া'],
    lastNames: ['আহমেদ', 'আলী', 'খান', 'রহমান', 'ইসলাম', 'হাসান', 'হোসেন', 'শেখ', 'চৌধুরী', 'মিয়া', 'খাতুন', 'বেগম', 'উদ্দিন', 'আক্তার', 'করিম', 'মোল্লা']
  },
  'ne_NP': {
    firstNames: ['राम', 'सीता', 'श्याम', 'गीता', 'हरि', 'लक्ष्मी', 'कृष्ण', 'राधा', 'गोपाल', 'सरस्वती', 'विष्णु', 'पार्वती', 'शिव', 'दुर्गा', 'ब्रह्म', 'काली'],
    lastNames: ['शर्मा', 'अधिकारी', 'गुरुङ', 'तामाङ', 'राई', 'लिम्बू', 'मगर', 'थापा', 'श्रेष्ठ', 'जोशी', 'पौडेल', 'खत्री', 'बस्नेत', 'पन्त', 'उपाध्याय', 'भट्टराई']
  },
  'fa_IR': {
    firstNames: ['محمد', 'فاطمه', 'علی', 'زهرا', 'حسن', 'مریم', 'حسین', 'آسیه', 'احمد', 'خدیجه', 'رضا', 'عایشه', 'مصطفی', 'زینب', 'عمر', 'رقیه'],
    lastNames: ['احمدی', 'محمدی', 'حسینی', 'رضایی', 'علوی', 'موسوی', 'کریمی', 'رحمانی', 'نوری', 'صادقی', 'حسنی', 'فاطمی', 'جعفری', 'طاهری', 'باقری', 'نجفی']
  },
  'he_IL': {
    firstNames: ['דוד', 'שרה', 'משה', 'רחל', 'יוסף', 'לאה', 'אברהם', 'רבקה', 'יעקב', 'מרים', 'יצחק', 'אסתר', 'שמואל', 'רות', 'אהרן', 'נעמי'],
    lastNames: ['כהן', 'לוי', 'מזרחי', 'פרידמן', 'דהן', 'אברמוביץ', 'ביטון', 'אוחנה', 'שפירא', 'פרץ', 'אזולאי', 'מלכה', 'חדד', 'בן דוד', 'יוסף', 'אליהו']
  },
  'tr_TR': {
    firstNames: ['Mehmet', 'Ayşe', 'Mustafa', 'Fatma', 'Ahmet', 'Hatice', 'Ali', 'Zeynep', 'Hüseyin', 'Emine', 'Hasan', 'Elif', 'İbrahim', 'Merve', 'Ömer', 'Özlem'],
    lastNames: ['Yılmaz', 'Kaya', 'Demir', 'Şahin', 'Çelik', 'Yıldız', 'Yıldırım', 'Öztürk', 'Aydin', 'Özdemir', 'Arslan', 'Doğan', 'Kılıç', 'Aslan', 'Çetin', 'Kara']
  },
  'kk_KZ': {
    firstNames: ['Нұрсұлтан', 'Айгүл', 'Ерлан', 'Гүлнар', 'Серік', 'Алма', 'Мұрат', 'Жанар', 'Асхат', 'Дина', 'Болат', 'Сауле', 'Ерболат', 'Айжан', 'Қайрат', 'Гүлмира'],
    lastNames: ['Назарбаев', 'Тоқаев', 'Мәсімов', 'Сағынтаев', 'Мәмин', 'Исекешев', 'Қасымов', 'Сарсенбаев', 'Жақсылықов', 'Тасмағамбетов', 'Ахметов', 'Құлибаев', 'Субханбердин', 'Есімов', 'Қожахметов', 'Байменов']
  },
  'mn_MN': {
    firstNames: ['Батбаяр', 'Оюунчимэг', 'Болд', 'Сайханцэцэг', 'Энхбаяр', 'Цэцэгмаа', 'Ганбаатар', 'Номинцэцэг', 'Мөнхбаяр', 'Алтанцэцэг', 'Пүрэвбаяр', 'Энхцэцэг', 'Батмөнх', 'Цагаанцэцэг', 'Энхболд', 'Мөнхцэцэг'],
    lastNames: ['Батбаяр', 'Энхбаяр', 'Болдбаатар', 'Ганбаатар', 'Мөнхбаяр', 'Пүрэвбаяр', 'Батмөнх', 'Энхболд', 'Цэндбаяр', 'Алтанбаяр', 'Сайханбаяр', 'Төмөрбаяр', 'Жавхланбаяр', 'Цэрэнбаяр', 'Дашбаяр', 'Нямбаяр']
  },
  'hy_AM': {
    firstNames: ['Արամ', 'Անահիտ', 'Դավիթ', 'Մարիամ', 'Հայկ', 'Արփինե', 'Վահան', 'Սիրանուշ', 'Արմեն', 'Նվարդ', 'Գարեգին', 'Զարուհի', 'Ռուբեն', 'Լուսինե', 'Արշակ', 'Գայանե'],
    lastNames: ['Հակոբյան', 'Պետրոսյան', 'Գրիգորյան', 'Ավետիսյան', 'Մարտիրոսյան', 'Ստեփանյան', 'Ղազարյան', 'Վարդանյան', 'Մանուկյան', 'Սարգսյան', 'Ալեքսանյան', 'Դավթյան', 'Մկրտչյան', 'Հովհաննիսյան', 'Բաղդասարյան', 'Կարապետյան']
  },
  'ka_GE': {
    firstNames: ['გიორგი', 'ნინო', 'დავით', 'მარიამ', 'ალექსანდრე', 'ანა', 'ირაკლი', 'ნათია', 'ლევან', 'თამარ', 'ზურაბ', 'ეკა', 'ნიკა', 'მაია', 'ვახტანგ', 'ნანა'],
    lastNames: ['ბერიძე', 'კვარაცხელია', 'ლობჟანიძე', 'ღლონტი', 'ჯაფარიძე', 'ღუდუშაური', 'ცხოვრებაძე', 'ღვინიაშვილი', 'ღარიბაშვილი', 'მამედოვი', 'ხაჩიძე', 'შენგელია', 'კაპანაძე', 'ჩხეიძე', 'მეგრელიშვილი', 'ღუღუნიშვილი']
  },

  // === 欧洲 ===
  // 英文姓名 (英国系)
  'en_GB': {
    firstNames: ['Oliver', 'Amelia', 'George', 'Isla', 'Harry', 'Ava', 'Noah', 'Mia', 'Jack', 'Isabella', 'Jacob', 'Sophia', 'Leo', 'Grace', 'Oscar', 'Lily'],
    lastNames: ['Smith', 'Jones', 'Taylor', 'Williams', 'Brown', 'Davies', 'Evans', 'Wilson', 'Thomas', 'Roberts', 'Johnson', 'Lewis', 'Walker', 'Robinson', 'Wood', 'Thompson']
  },  
  // 北美英文姓名
  'en_US': {
    firstNames: ['James', 'Mary', 'John', 'Patricia', 'Robert', 'Jennifer', 'Michael', 'Linda', 'William', 'Elizabeth', 'David', 'Barbara', 'Richard', 'Susan', 'Joseph', 'Jessica'],
    lastNames: ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson', 'Thomas']
  },
  'en_CA': {
    firstNames: ['Liam', 'Emma', 'Noah', 'Olivia', 'William', 'Ava', 'James', 'Isabella', 'Benjamin', 'Sophia', 'Lucas', 'Charlotte', 'Henry', 'Mia', 'Alexander', 'Amelia'],
    lastNames: ['Smith', 'Brown', 'Tremblay', 'Martin', 'Roy', 'Wilson', 'MacDonald', 'Johnson', 'Taylor', 'Anderson', 'Campbell', 'Lee', 'White', 'Thompson', 'Moore', 'Young']
  },
  'fr_CA': {
    firstNames: ['Félix', 'Emma', 'William', 'Olivia', 'Liam', 'Charlotte', 'Noah', 'Alice', 'Thomas', 'Béatrice', 'Jacob', 'Rosalie', 'Raphaël', 'Juliette', 'Antoine', 'Zoé'],
    lastNames: ['Tremblay', 'Gagnon', 'Roy', 'Côté', 'Bouchard', 'Gauthier', 'Morin', 'Lavoie', 'Fortin', 'Gagné', 'Ouellet', 'Pelletier', 'Bélanger', 'Lévesque', 'Bergeron', 'Leblanc']
  },
  
  // 德文姓名
  'de_DE': {
    firstNames: ['Ben', 'Emma', 'Paul', 'Mia', 'Leon', 'Hannah', 'Finn', 'Sofia', 'Noah', 'Emilia', 'Louis', 'Lina', 'Henry', 'Marie', 'Felix', 'Lea'],
    lastNames: ['Müller', 'Schmidt', 'Schneider', 'Fischer', 'Weber', 'Meyer', 'Wagner', 'Becker', 'Schulz', 'Hoffmann', 'Schäfer', 'Koch', 'Bauer', 'Richter', 'Klein', 'Wolf']
  },
  'de_AT': {
    firstNames: ['Maximilian', 'Anna', 'Alexander', 'Laura', 'Paul', 'Lena', 'Elias', 'Emma', 'Jakob', 'Leonie', 'David', 'Sophie', 'Noah', 'Marie', 'Simon', 'Johanna'],
    lastNames: ['Gruber', 'Huber', 'Bauer', 'Wagner', 'Müller', 'Pichler', 'Steiner', 'Moser', 'Mayer', 'Hofer', 'Leitner', 'Berger', 'Fuchs', 'Eder', 'Fischer', 'Schmid']
  },
  'de_CH': {
    firstNames: ['Noah', 'Mia', 'Liam', 'Emma', 'Matteo', 'Sofia', 'Leon', 'Lina', 'Gabriel', 'Ella', 'Luis', 'Lia', 'David', 'Alina', 'Elias', 'Laura'],
    lastNames: ['Müller', 'Meier', 'Schmid', 'Keller', 'Weber', 'Huber', 'Schneider', 'Meyer', 'Steiner', 'Fischer', 'Gerber', 'Brunner', 'Baumann', 'Frei', 'Zimmermann', 'Moser']
  },
  
  // 法文姓名
  'fr_FR': {
    firstNames: ['Louis', 'Emma', 'Gabriel', 'Jade', 'Raphaël', 'Louise', 'Arthur', 'Alice', 'Lucas', 'Chloé', 'Adam', 'Lina', 'Hugo', 'Rose', 'Maël', 'Anna'],
    lastNames: ['Martin', 'Bernard', 'Thomas', 'Petit', 'Robert', 'Richard', 'Durand', 'Dubois', 'Moreau', 'Laurent', 'Simon', 'Michel', 'Lefebvre', 'Leroy', 'Roux', 'David']
  },
  'fr_CH': {
    firstNames: ['Gabriel', 'Emma', 'Liam', 'Chloé', 'Noah', 'Léa', 'Arthur', 'Zoé', 'Louis', 'Alice', 'Jules', 'Camille', 'Adam', 'Inès', 'Lucas', 'Manon'],
    lastNames: ['Müller', 'Schmid', 'Schneider', 'Meyer', 'Weber', 'Huber', 'Martin', 'Bernard', 'Dubois', 'Thomas', 'Robert', 'Richard', 'Petit', 'Durand', 'Leroy', 'Moreau']
  },
  'fr_BE': {
    firstNames: ['Arthur', 'Emma', 'Louis', 'Olivia', 'Noah', 'Louise', 'Gabriel', 'Alice', 'Jules', 'Chloé', 'Adam', 'Camille', 'Lucas', 'Léa', 'Hugo', 'Zoé'],
    lastNames: ['Peeters', 'Janssens', 'Maes', 'Jacobs', 'Mertens', 'Willems', 'Claes', 'Goossens', 'Wouters', 'De Smet', 'De Meyer', 'Pauwels', 'De Cock', 'Smet', 'Vermeulen', 'Van Den Berg']
  },  
  // 意大利文姓名
  'it_IT': {
    firstNames: ['Francesco', 'Sofia', 'Alessandro', 'Giulia', 'Lorenzo', 'Aurora', 'Leonardo', 'Alice', 'Andrea', 'Ginevra', 'Gabriele', 'Emma', 'Matteo', 'Giorgia', 'Riccardo', 'Beatrice'],
    lastNames: ['Rossi', 'Russo', 'Ferrari', 'Esposito', 'Bianchi', 'Romano', 'Colombo', 'Ricci', 'Marino', 'Greco', 'Bruno', 'Gallo', 'Conti', 'De Luca', 'Mancini', 'Costa']
  },
  'it_CH': {
    firstNames: ['Leonardo', 'Sofia', 'Francesco', 'Giulia', 'Alessandro', 'Aurora', 'Lorenzo', 'Alice', 'Matteo', 'Emma', 'Andrea', 'Ginevra', 'Gabriele', 'Beatrice', 'Riccardo', 'Giorgia'],
    lastNames: ['Bernasconi', 'Cattaneo', 'Villa', 'Ferrari', 'Colombo', 'Bianchi', 'Rossi', 'Russo', 'Martini', 'Galli', 'Conti', 'Romano', 'Ricci', 'Greco', 'Marino', 'Bruno']
  },
  
  // 西班牙文姓名
  'es_ES': {
    firstNames: ['Hugo', 'Lucía', 'Martín', 'María', 'Daniel', 'Paula', 'Pablo', 'Emma', 'Alejandro', 'Daniela', 'Adrián', 'Carla', 'Álvaro', 'Sara', 'Manuel', 'Sofía'],
    lastNames: ['García', 'Rodríguez', 'González', 'Fernández', 'López', 'Martínez', 'Sánchez', 'Pérez', 'Gómez', 'Martín', 'Jiménez', 'Ruiz', 'Hernández', 'Díaz', 'Moreno', 'Muñoz']
  },
  
  // 荷兰文姓名
  'nl_NL': {
    firstNames: ['Daan', 'Emma', 'Lucas', 'Tess', 'Milan', 'Sophie', 'Sem', 'Fenna', 'Levi', 'Zoë', 'Finn', 'Liv', 'Noud', 'Nora', 'Mason', 'Mila'],
    lastNames: ['De Jong', 'Jansen', 'De Vries', 'Van Den Berg', 'Van Dijk', 'Bakker', 'Janssen', 'Visser', 'Smit', 'Meijer', 'De Boer', 'Mulder', 'De Groot', 'Bos', 'Vos', 'Peters']
  },
  'nl_BE': {
    firstNames: ['Arthur', 'Emma', 'Noah', 'Olivia', 'Louis', 'Louise', 'Jules', 'Alice', 'Gabriel', 'Chloé', 'Adam', 'Camille', 'Lucas', 'Léa', 'Hugo', 'Zoé'],
    lastNames: ['Peeters', 'Janssens', 'Maes', 'Jacobs', 'Mertens', 'Willems', 'Claes', 'Goossens', 'Wouters', 'De Smet', 'De Meyer', 'Pauwels', 'De Cock', 'Smet', 'Vermeulen', 'Van Den Berg']
  },
  
  // 葡萄牙文姓名
  'pt_PT': {
    firstNames: ['Francisco', 'Leonor', 'João', 'Matilde', 'Afonso', 'Beatriz', 'Duarte', 'Carolina', 'Gonçalo', 'Mariana', 'Tomás', 'Inês', 'Miguel', 'Ana', 'Pedro', 'Margarida'],
    lastNames: ['Silva', 'Santos', 'Ferreira', 'Pereira', 'Oliveira', 'Costa', 'Rodrigues', 'Martins', 'Jesus', 'Sousa', 'Fernandes', 'Gonçalves', 'Gomes', 'Lopes', 'Marques', 'Alves']
  },
  'pt_BR': {
    firstNames: ['Miguel', 'Alice', 'Arthur', 'Sophia', 'Bernardo', 'Helena', 'Heitor', 'Valentina', 'Davi', 'Laura', 'Lorenzo', 'Isabella', 'Théo', 'Manuela', 'Pedro', 'Júlia'],
    lastNames: ['Silva', 'Santos', 'Oliveira', 'Souza', 'Rodrigues', 'Ferreira', 'Alves', 'Pereira', 'Lima', 'Gomes', 'Ribeiro', 'Carvalho', 'Almeida', 'Lopes', 'Soares', 'Fernandes']
  },  
  // 俄文姓名
  'ru_RU': {
    firstNames: ['Александр', 'Анна', 'Михаил', 'Елена', 'Максим', 'Ольга', 'Артём', 'Татьяна', 'Дмитрий', 'Наталья', 'Никита', 'Ирина', 'Иван', 'Светлана', 'Алексей', 'Мария'],
    lastNames: ['Иванов', 'Смирнов', 'Кузнецов', 'Попов', 'Васильев', 'Петров', 'Соколов', 'Михайлов', 'Новиков', 'Фёдоров', 'Морозов', 'Волков', 'Алексеев', 'Лебедев', 'Семёнов', 'Егоров']
  },
  
  // 北欧姓名
  'sv_SE': {
    firstNames: ['William', 'Alice', 'Liam', 'Maja', 'Noah', 'Elsa', 'Hugo', 'Alma', 'Oliver', 'Astrid', 'Oscar', 'Vera', 'Lucas', 'Ebba', 'Elias', 'Freja'],
    lastNames: ['Andersson', 'Johansson', 'Karlsson', 'Nilsson', 'Eriksson', 'Larsson', 'Olsson', 'Persson', 'Svensson', 'Gustafsson', 'Pettersson', 'Jonsson', 'Jansson', 'Hansson', 'Bengtsson', 'Jönsson']
  },
  'nb_NO': {
    firstNames: ['Jakob', 'Emma', 'Emil', 'Nora', 'Oliver', 'Ella', 'William', 'Maja', 'Lucas', 'Emilie', 'Filip', 'Sofie', 'Liam', 'Leah', 'Henrik', 'Sara'],
    lastNames: ['Hansen', 'Johansen', 'Olsen', 'Larsen', 'Andersen', 'Pedersen', 'Nilsen', 'Kristiansen', 'Jensen', 'Karlsen', 'Johnsen', 'Pettersen', 'Eriksen', 'Berg', 'Haugen', 'Hagen']
  },
  'da_DK': {
    firstNames: ['William', 'Emma', 'Oliver', 'Ida', 'Noah', 'Clara', 'Oscar', 'Laura', 'Lucas', 'Mathilde', 'Carl', 'Sofia', 'Victor', 'Agnes', 'Magnus', 'Alma'],
    lastNames: ['Nielsen', 'Jensen', 'Hansen', 'Pedersen', 'Andersen', 'Christensen', 'Larsen', 'Sørensen', 'Rasmussen', 'Jørgensen', 'Petersen', 'Madsen', 'Kristensen', 'Olsen', 'Thomsen', 'Christiansen']
  },
  'fi_FI': {
    firstNames: ['Väinö', 'Aino', 'Eino', 'Helmi', 'Onni', 'Kerttu', 'Leevi', 'Siiri', 'Elias', 'Aada', 'Oliver', 'Lilja', 'Leo', 'Venla', 'Niilo', 'Pihla'],
    lastNames: ['Korhonen', 'Virtanen', 'Mäkinen', 'Nieminen', 'Mäkelä', 'Hämäläinen', 'Laine', 'Heikkinen', 'Koskinen', 'Järvinen', 'Lehtonen', 'Lehtinen', 'Saarinen', 'Salminen', 'Heinonen', 'Niemi']
  },
  'is_IS': {
    firstNames: ['Aron', 'Guðrún', 'Sigurður', 'Anna', 'Gunnar', 'Kristín', 'Ólafur', 'Margrét', 'Einar', 'Sigríður', 'Þórður', 'Helga', 'Jón', 'Ragnhildur', 'Magnús', 'Ingibjörg'],
    lastNames: ['Jónsson', 'Sigurðsson', 'Guðmundsson', 'Einarsson', 'Magnússon', 'Ólafsson', 'Þórsson', 'Kristjánsson', 'Arnarsson', 'Gunnarsson', 'Stefánsson', 'Ragnarsson', 'Björnsson', 'Þorsteinsson', 'Árnason', 'Friðriksson']
  },
  
  // 东欧姓名
  'pl_PL': {
    firstNames: ['Antoni', 'Zuzanna', 'Jan', 'Julia', 'Aleksander', 'Zofia', 'Franciszek', 'Hanna', 'Jakub', 'Maya', 'Leon', 'Lena', 'Mikołaj', 'Alicja', 'Stanisław', 'Amelia'],
    lastNames: ['Nowak', 'Kowalski', 'Wiśniewski', 'Wójcik', 'Kowalczyk', 'Kamiński', 'Lewandowski', 'Zieliński', 'Szymański', 'Woźniak', 'Dąbrowski', 'Kozłowski', 'Jankowski', 'Mazur', 'Kwiatkowski', 'Krawczyk']
  },
  'cs_CZ': {
    firstNames: ['Jakub', 'Tereza', 'Jan', 'Anna', 'Tomáš', 'Natálie', 'Adam', 'Adéla', 'Matěj', 'Karolína', 'Vojtěch', 'Barbora', 'Lukáš', 'Klára', 'David', 'Eliška'],
    lastNames: ['Novák', 'Svoboda', 'Novotný', 'Dvořák', 'Černý', 'Procházka', 'Kučera', 'Veselý', 'Horák', 'Němec', 'Pokorný', 'Pospíšil', 'Hájek', 'Jelínek', 'Král', 'Růžička']
  },
  'sk_SK': {
    firstNames: ['Jakub', 'Sofia', 'Tomáš', 'Emma', 'Adam', 'Nina', 'Matej', 'Viktória', 'Samuel', 'Natália', 'David', 'Lea', 'Daniel', 'Sára', 'Martin', 'Ema'],
    lastNames: ['Horváth', 'Kováč', 'Varga', 'Tóth', 'Nagy', 'Baláž', 'Szabó', 'Molnár', 'Simon', 'Lukáč', 'Takáč', 'Gál', 'Kočiš', 'Michal', 'Zeman', 'Čech']
  },  'hu_HU': {
    firstNames: ['Bence', 'Anna', 'Máté', 'Emma', 'Levente', 'Hanna', 'Dávid', 'Luca', 'Ádám', 'Lili', 'Zoltán', 'Viktória', 'Péter', 'Réka', 'Gergő', 'Sára'],
    lastNames: ['Nagy', 'Kovács', 'Tóth', 'Szabó', 'Horváth', 'Varga', 'Kiss', 'Molnár', 'Németh', 'Farkas', 'Balogh', 'Papp', 'Takács', 'Juhász', 'Lakatos', 'Mészáros']
  },
  'ro_RO': {
    firstNames: ['Andrei', 'Maria', 'Alexandru', 'Elena', 'Mihai', 'Ana', 'Stefan', 'Ioana', 'Radu', 'Andreea', 'Cristian', 'Alina', 'Adrian', 'Cristina', 'Florin', 'Diana'],
    lastNames: ['Popescu', 'Ionescu', 'Popa', 'Radu', 'Stoica', 'Stan', 'Dumitrescu', 'Dima', 'Constantinescu', 'Marin', 'Nistor', 'Florea', 'Georgescu', 'Tomescu', 'Mocanu', 'Barbu']
  },
  'ro_MD': {
    firstNames: ['Ion', 'Maria', 'Vasile', 'Elena', 'Nicolae', 'Ana', 'Gheorghe', 'Tatiana', 'Sergiu', 'Natalia', 'Andrei', 'Svetlana', 'Victor', 'Irina', 'Pavel', 'Oxana'],
    lastNames: ['Rusu', 'Moraru', 'Botnaru', 'Ciobanu', 'Cojocaru', 'Ungureanu', 'Cazacu', 'Munteanu', 'Lungu', 'Croitoru', 'Rotaru', 'Pascaru', 'Coșciug', 'Țurcanu', 'Gînju', 'Bivol']
  },
  'bg_BG': {
    firstNames: ['Александър', 'Мария', 'Георги', 'Елена', 'Димитър', 'Анна', 'Николай', 'Ивана', 'Иван', 'Петя', 'Стефан', 'Радка', 'Петър', 'Надежда', 'Христо', 'Веселина'],
    lastNames: ['Иванов', 'Георгиев', 'Димитров', 'Петров', 'Николов', 'Христов', 'Стоянов', 'Атанасов', 'Василев', 'Тодоров', 'Ангелов', 'Костов', 'Маринов', 'Станев', 'Русев', 'Колев']
  },
  'hr_HR': {
    firstNames: ['Luka', 'Petra', 'David', 'Ana', 'Mateo', 'Ema', 'Filip', 'Sara', 'Jakov', 'Lucija', 'Noa', 'Mia', 'Leon', 'Nika', 'Roko', 'Lana'],
    lastNames: ['Horvat', 'Novak', 'Marić', 'Petrović', 'Jurić', 'Kovačić', 'Babić', 'Knežević', 'Pavlović', 'Tomić', 'Matić', 'Božić', 'Blažević', 'Grgić', 'Vidović', 'Šimić']
  },
  'sl_SI': {
    firstNames: ['Luka', 'Ema', 'Jakob', 'Sara', 'Mark', 'Zala', 'Žan', 'Lara', 'Tim', 'Nina', 'Gal', 'Eva', 'Nik', 'Maja', 'Jan', 'Pia'],
    lastNames: ['Novak', 'Horvat', 'Krajnc', 'Zupančič', 'Potočnik', 'Kovačič', 'Mlakar', 'Kos', 'Vidmar', 'Golob', 'Kozole', 'Turk', 'Božič', 'Hribar', 'Kastelic', 'Oblak']
  },
  'sr_RS': {
    firstNames: ['Никола', 'Милица', 'Марко', 'Ана', 'Стефан', 'Јована', 'Лука', 'Марија', 'Филип', 'Тамара', 'Александар', 'Андреа', 'Немања', 'Сара', 'Милош', 'Анђела'],
    lastNames: ['Јовановић', 'Петровић', 'Николић', 'Марковић', 'Ђорђевић', 'Стојановић', 'Илић', 'Станковић', 'Павловић', 'Милошевић', 'Живковић', 'Томић', 'Ђукић', 'Радовановић', 'Костић', 'Симић']
  },
  'sr_Latn_RS': {
    firstNames: ['Nikola', 'Milica', 'Marko', 'Ana', 'Stefan', 'Jovana', 'Luka', 'Marija', 'Filip', 'Tamara', 'Aleksandar', 'Andrea', 'Nemanja', 'Sara', 'Miloš', 'Anđela'],
    lastNames: ['Jovanović', 'Petrović', 'Nikolić', 'Marković', 'Đorđević', 'Stojanović', 'Ilić', 'Stanković', 'Pavlović', 'Milošević', 'Živković', 'Tomić', 'Đukić', 'Radovanović', 'Kostić', 'Simić']
  },
  'sr_Cyrl_RS': {
    firstNames: ['Никола', 'Милица', 'Марко', 'Ана', 'Стефан', 'Јована', 'Лука', 'Марија', 'Филип', 'Тамара', 'Александар', 'Андреа', 'Немања', 'Сара', 'Милош', 'Анђела'],
    lastNames: ['Јовановић', 'Петровић', 'Николић', 'Марковић', 'Ђорђевић', 'Стојановић', 'Илић', 'Станковић', 'Павловић', 'Милошевић', 'Живковић', 'Томић', 'Ђукић', 'Радовановић', 'Костић', 'Симић']
  },
  'me_ME': {
    firstNames: ['Nikola', 'Milica', 'Marko', 'Ana', 'Stefan', 'Jovana', 'Luka', 'Marija', 'Filip', 'Tamara', 'Aleksandar', 'Andrea', 'Nemanja', 'Sara', 'Miloš', 'Anđela'],
    lastNames: ['Popović', 'Petrović', 'Nikolić', 'Marković', 'Đorđević', 'Stojanović', 'Ilić', 'Stanković', 'Pavlović', 'Milošević', 'Živković', 'Tomić', 'Đukić', 'Radovanović', 'Kostić', 'Simić']
  },  'lt_LT': {
    firstNames: ['Lukas', 'Emilija', 'Matas', 'Sofija', 'Nojus', 'Liepa', 'Dovydas', 'Gabija', 'Kajus', 'Austėja', 'Rokas', 'Ieva', 'Tomas', 'Urtė', 'Dominykas', 'Kotryna'],
    lastNames: ['Kazlauskas', 'Petrauskas', 'Jankauskas', 'Stankevičius', 'Vasiliauskas', 'Žukauskas', 'Butkus', 'Paulauskas', 'Urbonas', 'Kavaliauskas', 'Rimkus', 'Laurinavičius', 'Mačiulis', 'Gudas', 'Navickas', 'Šimkus']
  },
  'lv_LV': {
    firstNames: ['Roberts', 'Sofija', 'Artūrs', 'Anna', 'Emīls', 'Elizabete', 'Markus', 'Marija', 'Aleksis', 'Viktōrija', 'Daniels', 'Katrīna', 'Rihards', 'Līva', 'Kristaps', 'Elīza'],
    lastNames: ['Bērziņš', 'Kalniņš', 'Liepiņš', 'Ozols', 'Krūmiņš', 'Zariņš', 'Pētersons', 'Jansons', 'Kļaviņš', 'Rozītis', 'Sproģis', 'Grants', 'Freibergs', 'Vītoliņš', 'Āboliņš', 'Dumpis']
  },
  'et_EE': {
    firstNames: ['Rasmus', 'Emma', 'Oliver', 'Sofia', 'Robin', 'Maria', 'Hugo', 'Anna', 'Mattias', 'Mia', 'Sebastian', 'Laura', 'Lucas', 'Lisette', 'Kevin', 'Hanna'],
    lastNames: ['Tamm', 'Saar', 'Sepp', 'Mägi', 'Kask', 'Kukk', 'Rebane', 'Ilves', 'Pärn', 'Koppel', 'Vask', 'Roos', 'Känd', 'Org', 'Kuusk', 'Laur']
  },
  'uk_UA': {
    firstNames: ['Олександр', 'Анна', 'Максим', 'Марія', 'Артем', 'Олена', 'Дмитро', 'Катерина', 'Андрій', 'Ірина', 'Сергій', 'Наталія', 'Володимир', 'Тетяна', 'Михайло', 'Оксана'],
    lastNames: ['Мельник', 'Шевченко', 'Бойко', 'Коваленко', 'Бондаренко', 'Ткаченко', 'Кравченко', 'Олійник', 'Шевчук', 'Поліщук', 'Лисенко', 'Гриценко', 'Руденко', 'Савченко', 'Петренко', 'Іваненко']
  },
  'el_GR': {
    firstNames: ['Γιάννης', 'Μαρία', 'Γιώργος', 'Ελένη', 'Δημήτρης', 'Κατερίνα', 'Νίκος', 'Άννα', 'Κώστας', 'Σοφία', 'Παναγιώτης', 'Βασιλική', 'Αντώνης', 'Χριστίνα', 'Μιχάλης', 'Ιωάννα'],
    lastNames: ['Παπαδόπουλος', 'Γεωργίου', 'Παπαγεωργίου', 'Δημητρίου', 'Κωνσταντίνου', 'Νικολάου', 'Ιωάννου', 'Παπαδάκης', 'Αντωνίου', 'Μιχαήλ', 'Στεφάνου', 'Χριστοδούλου', 'Παπαδημητρίου', 'Αθανασίου', 'Βασιλείου', 'Γρηγορίου']
  },
  'el_CY': {
    firstNames: ['Ανδρέας', 'Μαρία', 'Γιώργος', 'Ελένη', 'Χρίστος', 'Άννα', 'Νίκος', 'Κατερίνα', 'Παναγιώτης', 'Σοφία', 'Μιχάλης', 'Χριστίνα', 'Κώστας', 'Ιωάννα', 'Αντώνης', 'Δέσποινα'],
    lastNames: ['Χαραλάμπους', 'Γεωργίου', 'Δημητρίου', 'Νικολάου', 'Ιωάννου', 'Κωνσταντίνου', 'Αντωνίου', 'Μιχαήλ', 'Στεφάνου', 'Χριστοδούλου', 'Αθανασίου', 'Βασιλείου', 'Γρηγορίου', 'Παπαδόπουλος', 'Σάββα', 'Λουκά']
  },
  'at_AT': {
    firstNames: ['Maximilian', 'Anna', 'Alexander', 'Laura', 'Paul', 'Lena', 'Elias', 'Emma', 'Jakob', 'Leonie', 'David', 'Sophie', 'Noah', 'Marie', 'Simon', 'Johanna'],
    lastNames: ['Gruber', 'Huber', 'Bauer', 'Wagner', 'Müller', 'Pichler', 'Steiner', 'Moser', 'Mayer', 'Hofer', 'Leitner', 'Berger', 'Fuchs', 'Eder', 'Fischer', 'Schmid']
  },

  // === 南美洲 ===
  'es_AR': {
    firstNames: ['Santiago', 'Emma', 'Mateo', 'Olivia', 'Benjamín', 'Martina', 'Nicolás', 'Sofia', 'Thiago', 'Isabella', 'Bautista', 'Mía', 'Lautaro', 'Catalina', 'Joaquín', 'Valentina'],
    lastNames: ['González', 'Rodríguez', 'Gómez', 'Fernández', 'López', 'Díaz', 'Martínez', 'Pérez', 'García', 'Martín', 'Sánchez', 'Romero', 'Ruiz', 'Vargas', 'Castro', 'Álvarez']
  },
  'es_PE': {
    firstNames: ['Sebastián', 'Sofía', 'Mateo', 'Valentina', 'Diego', 'Emma', 'Nicolás', 'Isabella', 'Adrián', 'Camila', 'Gabriel', 'Martina', 'Joaquín', 'Luciana', 'Santiago', 'Antonella'],
    lastNames: ['García', 'López', 'Rodríguez', 'Pérez', 'González', 'Sánchez', 'Ramírez', 'Torres', 'Flores', 'Rivera', 'Gómez', 'Díaz', 'Cruz', 'Morales', 'Ortiz', 'Gutiérrez']
  },
  'es_VE': {
    firstNames: ['Santiago', 'Sofía', 'Mateo', 'Valentina', 'Sebastián', 'Emma', 'Diego', 'Isabella', 'Nicolás', 'Camila', 'Gabriel', 'Martina', 'Adrián', 'Luciana', 'Joaquín', 'Antonella'],
    lastNames: ['García', 'Rodríguez', 'González', 'Hernández', 'López', 'Martínez', 'Pérez', 'Sánchez', 'Ramírez', 'Torres', 'Flores', 'Rivera', 'Gómez', 'Díaz', 'Morales', 'Gutiérrez']
  },  // === 非洲 ===
  'ar_EG': {
    firstNames: ['محمد', 'فاطمة', 'أحمد', 'عائشة', 'علي', 'خديجة', 'حسن', 'زينب', 'عمر', 'مريم', 'يوسف', 'أسماء', 'إبراهيم', 'نور', 'عبدالله', 'سارة'],
    lastNames: ['محمد', 'أحمد', 'علي', 'حسن', 'إبراهيم', 'عبدالله', 'محمود', 'عبدالرحمن', 'خالد', 'عمر', 'يوسف', 'السيد', 'عبدالعزيز', 'مصطفى', 'عثمان', 'صالح']
  },
  'en_ZA': {
    firstNames: ['Liam', 'Emma', 'Noah', 'Olivia', 'William', 'Ava', 'James', 'Isabella', 'Benjamin', 'Sophia', 'Lucas', 'Charlotte', 'Henry', 'Mia', 'Alexander', 'Amelia'],
    lastNames: ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Miller', 'Davis', 'Garcia', 'Rodriguez', 'Wilson', 'Martinez', 'Anderson', 'Taylor', 'Thomas', 'Hernandez', 'Moore']
  },
  'en_NG': {
    firstNames: ['Chinedu', 'Ngozi', 'Emeka', 'Adaeze', 'Kelechi', 'Chioma', 'Obinna', 'Ifeoma', 'Chukwudi', 'Amaka', 'Ikechukwu', 'Nneka', 'Chidi', 'Chiamaka', 'Nnamdi', 'Chinelo'],
    lastNames: ['Okafor', 'Okwu', 'Nwankwo', 'Eze', 'Okonkwo', 'Okoro', 'Nwosu', 'Anyanwu', 'Obi', 'Chukwu', 'Nwachukwu', 'Okeke', 'Ugwu', 'Nwokolo', 'Onwuegbuzie', 'Okafor']
  },
  'en_UG': {
    firstNames: ['John', 'Mary', 'Paul', 'Sarah', 'David', 'Grace', 'Peter', 'Ruth', 'James', 'Rebecca', 'Samuel', 'Esther', 'Joseph', 'Rachel', 'Daniel', 'Miriam'],
    lastNames: ['Mukasa', 'Namukasa', 'Ssali', 'Nakato', 'Kato', 'Nakamya', 'Ssekandi', 'Namatovu', 'Wasswa', 'Nabirye', 'Kiprotich', 'Akello', 'Okello', 'Auma', 'Ochieng', 'Atieno']
  },

  // === 大洋洲 ===
  'en_AU': {
    firstNames: ['William', 'Charlotte', 'Oliver', 'Olivia', 'Jack', 'Amelia', 'Henry', 'Isla', 'Lucas', 'Mia', 'Thomas', 'Grace', 'Ethan', 'Zoe', 'James', 'Sophie'],
    lastNames: ['Smith', 'Jones', 'Williams', 'Brown', 'Wilson', 'Taylor', 'Johnson', 'White', 'Martin', 'Anderson', 'Thompson', 'Nguyen', 'Thomas', 'Walker', 'Harris', 'Lee']
  },
  'en_NZ': {
    firstNames: ['Oliver', 'Charlotte', 'Jack', 'Amelia', 'William', 'Isla', 'James', 'Olivia', 'Noah', 'Emily', 'Lucas', 'Mia', 'Henry', 'Grace', 'Leo', 'Zoe'],
    lastNames: ['Smith', 'Brown', 'Wilson', 'Taylor', 'Anderson', 'Thomas', 'Jackson', 'White', 'Harris', 'Martin', 'Thompson', 'Garcia', 'Martinez', 'Robinson', 'Clark', 'Rodriguez']
  },

  // === 中东 ===
  'ar_SA': {
    firstNames: ['محمد', 'فاطمة', 'عبدالله', 'عائشة', 'أحمد', 'خديجة', 'علي', 'زينب', 'عمر', 'مريم', 'حسن', 'أسماء', 'يوسف', 'نور', 'إبراهيم', 'سارة'],
    lastNames: ['آل سعود', 'العتيبي', 'الغامدي', 'القحطاني', 'الحربي', 'المطيري', 'العنزي', 'الدوسري', 'الشهري', 'الزهراني', 'الخالدي', 'العسيري', 'الرشيد', 'البقمي', 'الجهني', 'السبيعي']
  },
  'ar_JO': {
    firstNames: ['محمد', 'فاطمة', 'أحمد', 'عائشة', 'علي', 'خديجة', 'حسن', 'زينب', 'عمر', 'مريم', 'يوسف', 'أسماء', 'إبراهيم', 'نور', 'عبدالله', 'سارة'],
    lastNames: ['الأردني', 'العبدالله', 'الخوري', 'النجار', 'الحداد', 'الطويل', 'القاسم', 'الشامي', 'البدوي', 'الفلسطيني', 'اللبناني', 'السوري', 'المصري', 'العراقي', 'اليمني', 'السعودي']
  }
};

// 地址数据库 - 按照国际标准7行格式
const ADDRESS_DATABASE: Record<string, {
  cities: string[],
  streets: string[],
  postalCodes: string[],
  states?: string[],
  districts: string[], // 区/县/地区
  format: 'standard' | 'reverse' // 地址格式：标准(小到大) 或 反向(大到小)
}> = {
  // 中国 - 格式：姓名/邮编/省市/街道/区县/国家/邮箱
  'zh_CN': {
    cities: ['北京市', '上海市', '广州市', '深圳市', '杭州市', '南京市', '武汉市', '成都市', '西安市', '重庆市', '天津市', '苏州市', '长沙市', '郑州市', '青岛市'],
    states: ['北京', '上海', '广东省', '广东省', '浙江省', '江苏省', '湖北省', '四川省', '陕西省', '重庆', '天津', '江苏省', '湖南省', '河南省', '山东省'],
    districts: ['朝阳区', '浦东新区', '天河区', '南山区', '西湖区', '鼓楼区', '武昌区', '锦江区', '雁塔区', '渝中区', '和平区', '姑苏区', '岳麓区', '金水区', '市南区'],
    streets: ['中山路', '人民路', '解放路', '建设路', '和平路', '胜利路', '文化路', '新华路', '光明路', '友谊路', '团结路', '幸福路', '民主路', '自由路', '繁荣路'],
    postalCodes: ['100000', '200000', '510000', '518000', '310000', '210000', '430000', '610000', '710000', '400000', '300000', '215000', '410000', '450000', '266000'],
    format: 'reverse' // 中国地址格式：大到小
  },
  
  // 台湾
  'zh_TW': {
    cities: ['台北市', '高雄市', '台中市', '台南市', '桃園市', '新竹市', '基隆市', '嘉義市', '台東縣', '花蓮縣', '宜蘭縣', '苗栗縣', '彰化縣', '南投縣', '雲林縣'],
    states: ['台北', '高雄', '台中', '台南', '桃園', '新竹', '基隆', '嘉義', '台東', '花蓮', '宜蘭', '苗栗', '彰化', '南投', '雲林'],
    districts: ['中正區', '前金區', '西區', '中西區', '桃園區', '東區', '仁愛區', '西區', '台東市', '花蓮市', '宜蘭市', '苗栗市', '彰化市', '南投市', '斗六市'],
    streets: ['中山路', '中正路', '民生路', '忠孝路', '仁愛路', '信義路', '和平路', '復興路', '建國路', '民權路', '光復路', '中華路', '成功路', '自由路', '博愛路'],
    postalCodes: ['100', '800', '400', '700', '330', '300', '200', '600', '950', '970', '260', '350', '500', '540', '630'],
    format: 'reverse'
  },

  // 日本 - 格式：姓名/邮编/都道府县/市区町村/街道/国家/邮箱
  'ja_JP': {
    cities: ['港区', '新宿区', '渋谷区', '世田谷区', '中央区', '千代田区', '品川区', '目黒区', '大田区', '江東区', '墨田区', '台東区', '荒川区', '足立区', '葛飾区'],
    states: ['東京都', '大阪府', '神奈川県', '愛知県', '北海道', '兵庫県', '京都府', '福岡県', '埼玉県', '千葉県', '広島県', '宮城県', '静岡県', '茨城県', '栃木県'],
    districts: ['赤坂', '青山', '麻布', '六本木', '銀座', '丸の内', '大手町', '新宿', '渋谷', '池袋', '上野', '浅草', '品川', '表参道', '原宿'],
    streets: ['一丁目', '二丁目', '三丁目', '四丁目', '五丁目', '六丁目', '七丁目', '八丁目', '九丁目', '十丁目', '十一丁目', '十二丁目', '十三丁目', '十四丁目', '十五丁目'],
    postalCodes: ['100-0001', '160-0022', '150-0002', '154-0017', '104-0061', '100-0014', '141-0001', '153-0063', '144-0052', '135-0016', '130-0005', '110-0005', '116-0002', '120-0005', '124-0006'],
    format: 'reverse'
  },

  // 韩国
  'ko_KR': {
    cities: ['서울시', '부산시', '대구시', '인천시', '광주시', '대전시', '울산시', '수원시', '고양시', '용인시', '성남시', '청주시', '안산시', '전주시', '천안시'],
    states: ['서울특별시', '부산광역시', '대구광역시', '인천광역시', '광주광역시', '대전광역시', '울산광역시', '경기도', '경기도', '경기도', '경기도', '충청북도', '경기도', '전라북도', '충청남도'],
    districts: ['강남구', '해운대구', '중구', '남동구', '서구', '유성구', '남구', '영통구', '덕양구', '기흥구', '분당구', '상당구', '단원구', '완산구', '동남구'],
    streets: ['강남대로', '테헤란로', '종로', '명동길', '홍대입구', '이태원로', '압구정로', '신촌로', '건대입구', '노원로', '송파대로', '영등포로', '마포대로', '서초대로', '잠실로'],
    postalCodes: ['06292', '48058', '41566', '22101', '61475', '34126', '44919', '16499', '10408', '16827', '13494', '28644', '15588', '54999', '31116'],
    format: 'reverse'
  },
  
  // 美国 - 格式：姓名/街道/城市/州/邮编/国家/邮箱
  'en_US': {
    cities: ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia', 'San Antonio', 'San Diego', 'Dallas', 'San Jose', 'Austin', 'Jacksonville', 'Fort Worth', 'Columbus', 'Charlotte'],
    states: ['NY', 'CA', 'IL', 'TX', 'AZ', 'PA', 'TX', 'CA', 'TX', 'CA', 'TX', 'FL', 'TX', 'OH', 'NC'],
    districts: ['Manhattan', 'Hollywood', 'Downtown', 'Downtown', 'Central City', 'Center City', 'Downtown', 'Downtown', 'Downtown', 'Downtown', 'Downtown', 'Downtown', 'Downtown', 'Downtown', 'Uptown'],
    streets: ['Main St', 'First St', 'Second St', 'Park Ave', 'Oak St', 'Pine St', 'Maple St', 'Cedar St', 'Elm St', 'Washington St', 'Lake St', 'Hill St', 'Church St', 'Spring St', 'Center St'],
    postalCodes: ['10001', '90210', '60601', '77001', '85001', '19101', '78201', '92101', '75201', '95101', '73301', '32099', '76101', '43085', '28201'],
    format: 'standard'
  },
  
  // 英国 - 格式：姓名/街道/城市/郡/邮编/国家/邮箱
  'en_GB': {
    cities: ['London', 'Birmingham', 'Manchester', 'Glasgow', 'Liverpool', 'Leeds', 'Sheffield', 'Edinburgh', 'Bristol', 'Cardiff', 'Leicester', 'Coventry', 'Bradford', 'Belfast', 'Nottingham'],
    states: ['Greater London', 'West Midlands', 'Greater Manchester', 'Glasgow City', 'Merseyside', 'West Yorkshire', 'South Yorkshire', 'City of Edinburgh', 'Bristol', 'Cardiff', 'Leicestershire', 'West Midlands', 'West Yorkshire', 'Belfast', 'Nottinghamshire'],
    districts: ['Westminster', 'City Centre', 'City Centre', 'City Centre', 'City Centre', 'City Centre', 'City Centre', 'Old Town', 'City Centre', 'City Centre', 'City Centre', 'City Centre', 'City Centre', 'City Centre', 'City Centre'],
    streets: ['High Street', 'Church Lane', 'Main Street', 'Park Road', 'Victoria Road', 'Green Lane', 'Manor Road', 'Church Street', 'Park Lane', 'Kings Road', 'Queens Road', 'Mill Lane', 'School Lane', 'New Road', 'Elm Grove'],
    postalCodes: ['SW1A 1AA', 'B1 1AA', 'M1 1AA', 'G1 1AA', 'L1 8JQ', 'LS1 1UR', 'S1 2HE', 'EH1 1YZ', 'BS1 4DJ', 'CF10 1BH', 'LE1 6ZG', 'CV1 1GF', 'BD1 1DB', 'BT1 1AA', 'NG1 1AA'],
    format: 'standard'
  },

  // 德国 - 格式：姓名/街道/邮编/城市/州/国家/邮箱
  'de_DE': {
    cities: ['Berlin', 'Hamburg', 'München', 'Köln', 'Frankfurt', 'Stuttgart', 'Düsseldorf', 'Dortmund', 'Essen', 'Leipzig', 'Bremen', 'Dresden', 'Hannover', 'Nürnberg', 'Duisburg'],
    states: ['Berlin', 'Hamburg', 'Bayern', 'Nordrhein-Westfalen', 'Hessen', 'Baden-Württemberg', 'Nordrhein-Westfalen', 'Nordrhein-Westfalen', 'Nordrhein-Westfalen', 'Sachsen', 'Bremen', 'Sachsen', 'Niedersachsen', 'Bayern', 'Nordrhein-Westfalen'],
    districts: ['Mitte', 'Altstadt', 'Altstadt', 'Altstadt', 'Innenstadt', 'Mitte', 'Altstadt', 'Innenstadt', 'Stadtmitte', 'Zentrum', 'Altstadt', 'Altstadt', 'Mitte', 'Altstadt', 'Altstadt'],
    streets: ['Hauptstraße', 'Schulstraße', 'Dorfstraße', 'Bahnhofstraße', 'Kirchstraße', 'Gartenstraße', 'Mühlenstraße', 'Lindenstraße', 'Bergstraße', 'Poststraße', 'Marktstraße', 'Friedhofstraße', 'Ringstraße', 'Waldstraße', 'Am Markt'],
    postalCodes: ['10115', '20095', '80331', '50667', '60311', '70173', '40213', '44135', '45127', '04109', '28195', '01067', '30159', '90402', '47051'],
    format: 'standard'
  },

  // 法国 - 格式：姓名/街道/邮编/城市/大区/国家/邮箱
  'fr_FR': {
    cities: ['Paris', 'Marseille', 'Lyon', 'Toulouse', 'Nice', 'Nantes', 'Strasbourg', 'Montpellier', 'Bordeaux', 'Lille', 'Rennes', 'Reims', 'Le Havre', 'Saint-Étienne', 'Toulon'],
    states: ['Île-de-France', 'Provence-Alpes-Côte d\'Azur', 'Auvergne-Rhône-Alpes', 'Occitanie', 'Provence-Alpes-Côte d\'Azur', 'Pays de la Loire', 'Grand Est', 'Occitanie', 'Nouvelle-Aquitaine', 'Hauts-de-France', 'Bretagne', 'Grand Est', 'Normandie', 'Auvergne-Rhône-Alpes', 'Provence-Alpes-Côte d\'Azur'],
    districts: ['1er arrondissement', '1er arrondissement', '1er arrondissement', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre'],
    streets: ['Rue de la Paix', 'Avenue des Champs-Élysées', 'Rue de Rivoli', 'Boulevard Saint-Germain', 'Rue du Faubourg Saint-Honoré', 'Avenue Montaigne', 'Rue Saint-Antoine', 'Boulevard Haussmann', 'Rue de la République', 'Avenue de la Liberté', 'Rue Victor Hugo', 'Place de la République', 'Rue Nationale', 'Avenue Jean Jaurès', 'Rue Gambetta'],
    postalCodes: ['75001', '13001', '69001', '31000', '06000', '44000', '67000', '34000', '33000', '59000', '35000', '51100', '76600', '42000', '83000'],
    format: 'standard'
  },

  // 印度 - 格式：姓名/街道/城市/州/邮编/国家/邮箱
  'en_IN': {
    cities: ['Mumbai', 'Delhi', 'Bangalore', 'Hyderabad', 'Chennai', 'Kolkata', 'Pune', 'Ahmedabad', 'Jaipur', 'Surat', 'Lucknow', 'Kanpur', 'Nagpur', 'Indore', 'Thane'],
    states: ['Maharashtra', 'Delhi', 'Karnataka', 'Telangana', 'Tamil Nadu', 'West Bengal', 'Maharashtra', 'Gujarat', 'Rajasthan', 'Gujarat', 'Uttar Pradesh', 'Uttar Pradesh', 'Maharashtra', 'Madhya Pradesh', 'Maharashtra'],
    districts: ['Andheri', 'Connaught Place', 'Koramangala', 'Banjara Hills', 'T. Nagar', 'Salt Lake', 'Koregaon Park', 'Satellite', 'C-Scheme', 'Adajan', 'Hazratganj', 'Civil Lines', 'Sadar', 'Vijay Nagar', 'Hiranandani'],
    streets: ['MG Road', 'Brigade Road', 'Commercial Street', 'Park Street', 'Linking Road', 'FC Road', 'SG Highway', 'MI Road', 'Ring Road', 'Mall Road', 'Station Road', 'Civil Lines', 'Residency Road', 'AB Road', 'Eastern Express Highway'],
    postalCodes: ['400001', '110001', '560001', '500001', '600001', '700001', '411001', '380001', '302001', '395001', '226001', '208001', '440001', '452001', '400601'],
    format: 'standard'
  },

  // 泰国 - 格式：姓名/街道/城市/府/邮编/国家/邮箱
  'th_TH': {
    cities: ['กรุงเทพมหานคร', 'เชียงใหม่', 'นครราชสีมา', 'ขอนแก่น', 'อุดรธานี', 'สุราษฎร์ธานี', 'ชลบุรี', 'นครศรีธรรมราช', 'เชียงราย', 'ลำปาง', 'อุบลราชธานี', 'ร้อยเอ็ด', 'สกลนคร', 'นครสวรรค์', 'ศรีสะเกษ'],
    states: ['กรุงเทพมหานคร', 'เชียงใหม่', 'นครราชสีมา', 'ขอนแก่น', 'อุดรธานี', 'สุราษฎร์ธานี', 'ชลบุรี', 'นครศรีธรรมราช', 'เชียงราย', 'ลำปาง', 'อุบลราชธานี', 'ร้อยเอ็ด', 'สกลนคร', 'นครสวรรค์', 'ศรีสะเกษ'],
    districts: ['วัฒนา', 'เมือง', 'เมือง', 'เมือง', 'เมือง', 'เมือง', 'เมือง', 'เมือง', 'เมือง', 'เมือง', 'เมือง', 'เมือง', 'เมือง', 'เมือง', 'เมือง'],
    streets: ['ถนนสุขุมวิท', 'ถนนพหลโยธิน', 'ถนนรัชดาภิเษก', 'ถนนเพชรบุรี', 'ถนนสีลม', 'ถนนสาทร', 'ถนนวิภาวดีรังสิต', 'ถนนลาดพร้าว', 'ถนนรามคำแหง', 'ถนนบางนา', 'ถนนเจริญกรุง', 'ถนนราชดำริ', 'ถนนปลวกแดง', 'ถนนมิตรภาพ', 'ถนนอโศก'],
    postalCodes: ['10110', '50000', '30000', '40000', '41000', '84000', '20000', '80000', '57000', '52000', '34000', '45000', '47000', '60000', '33000'],
    format: 'reverse'
  },

  // 越南 - 格式：姓名/街道/区/城市/省/邮编/国家/邮箱
  'vi_VN': {
    cities: ['TP. Hồ Chí Minh', 'Hà Nội', 'Đà Nẵng', 'Hải Phòng', 'Cần Thơ', 'Biên Hòa', 'Huế', 'Nha Trang', 'Buôn Ma Thuột', 'Vũng Tàu', 'Quy Nhon', 'Nam Định', 'Long Xuyên', 'Rạch Giá', 'Thái Nguyên'],
    states: ['TP. Hồ Chí Minh', 'Hà Nội', 'Đà Nẵng', 'Hải Phòng', 'Cần Thơ', 'Đồng Nai', 'Thừa Thiên Huế', 'Khánh Hòa', 'Đắk Lắk', 'Bà Rịa-Vũng Tàu', 'Bình Định', 'Nam Định', 'An Giang', 'Kiên Giang', 'Thái Nguyên'],
    districts: ['Quận 1', 'Quận 3', 'Quận 5', 'Ba Đình', 'Hoàn Kiếm', 'Hai Châu', 'Hồng Bàng', 'Ninh Kiều', 'Biên Hòa', 'Huế', 'Nha Trang', 'Buôn Ma Thuột', 'Vũng Tàu', 'Quy Nhon', 'Nam Định'],
    streets: ['Nguyễn Trãi', 'Lê Lợi', 'Trần Hưng Đạo', 'Hai Bà Trưng', 'Điện Biên Phủ', 'Võ Văn Kiệt', 'Cách Mạng Tháng 8', 'Nguyễn Huệ', 'Đồng Khởi', 'Nam Kỳ Khởi Nghĩa', 'Pasteur', 'Lý Tự Trọng', 'Nguyễn Thị Minh Khai', 'Phạm Ngũ Lão', 'Bùi Viện'],
    postalCodes: ['700000', '100000', '550000', '180000', '900000', '810000', '530000', '650000', '630000', '790000', '590000', '420000', '880000', '920000', '250000'],
    format: 'standard'
  },

  // 马来西亚 - 格式：姓名/街道/邮编/城市/州/国家/邮箱
  'ms_MY': {
    cities: ['Kuala Lumpur', 'Selangor', 'Johor Bahru', 'Penang', 'Ipoh', 'Kuching', 'Kota Kinabalu', 'Shah Alam', 'Malacca', 'Alor Setar', 'Kuantan', 'Kota Bharu', 'Miri', 'Sandakan', 'Tawau'],
    states: ['Kuala Lumpur', 'Selangor', 'Johor', 'Penang', 'Perak', 'Sarawak', 'Sabah', 'Selangor', 'Malacca', 'Kedah', 'Pahang', 'Kelantan', 'Sarawak', 'Sabah', 'Sabah'],
    districts: ['KLCC', 'Petaling Jaya', 'Johor Bahru', 'Georgetown', 'Ipoh', 'Kuching', 'Kota Kinabalu', 'Shah Alam', 'Malacca', 'Alor Setar', 'Kuantan', 'Kota Bharu', 'Miri', 'Sandakan', 'Tawau'],
    streets: ['Jalan Bukit Bintang', 'Jalan Ampang', 'Jalan Raja Chulan', 'Jalan Tun Razak', 'Jalan Imbi', 'Jalan Pudu', 'Jalan Chow Kit', 'Jalan Tuanku Abdul Rahman', 'Jalan Masjid India', 'Jalan Petaling', 'Jalan Hang Tuah', 'Jalan Sultan Ismail', 'Jalan Dang Wangi', 'Jalan Davis', 'Jalan Tengku Abdul Rahman'],
    postalCodes: ['50000', '40000', '80000', '10000', '30000', '93000', '88000', '40000', '75000', '05000', '25000', '15000', '98000', '90000', '91000'],
    format: 'standard'
  },

  // 印尼 - 格式：姓名/街道/区/城市/邮编/省/国家/邮箱
  'id_ID': {
    cities: ['Jakarta', 'Surabaya', 'Bandung', 'Medan', 'Semarang', 'Makassar', 'Palembang', 'Tangerang', 'Depok', 'Bekasi', 'Padang', 'Denpasar', 'Malang', 'Samarinda', 'Banjarmasin'],
    states: ['DKI Jakarta', 'Jawa Timur', 'Jawa Barat', 'Sumatera Utara', 'Jawa Tengah', 'Sulawesi Selatan', 'Sumatera Selatan', 'Banten', 'Jawa Barat', 'Jawa Barat', 'Sumatera Barat', 'Bali', 'Jawa Timur', 'Kalimantan Timur', 'Kalimantan Selatan'],
    districts: ['Jakarta Pusat', 'Surabaya Pusat', 'Bandung Wetan', 'Medan Kota', 'Semarang Tengah', 'Makassar', 'Ilir Timur', 'Tangerang Kota', 'Depok', 'Bekasi Kota', 'Padang Barat', 'Denpasar Selatan', 'Klojen', 'Samarinda Kota', 'Banjarmasin Tengah'],
    streets: ['Jalan Thamrin', 'Jalan Sudirman', 'Jalan Gatot Subroto', 'Jalan Kuningan', 'Jalan Kemang', 'Jalan Senopati', 'Jalan Menteng', 'Jalan Cikini', 'Jalan Sabang', 'Jalan Jaksa', 'Jalan Malioboro', 'Jalan Asia Afrika', 'Jalan Braga', 'Jalan Dago', 'Jalan Setiabudhi'],
    postalCodes: ['10110', '60271', '40111', '20111', '50241', '90111', '30111', '15111', '16411', '17141', '25111', '80221', '65111', '75111', '70111'],
    format: 'standard'
  },

  // 香港 - 格式：姓名/单位/楼层/建筑/街道/区/香港/邮箱 (无邮编)
  'en_HK': {
    cities: ['Central', 'Wan Chai', 'Causeway Bay', 'Tsim Sha Tsui', 'Mong Kok', 'Yau Ma Tei', 'Jordan', 'Admiralty', 'Sheung Wan', 'Mid-Levels', 'Happy Valley', 'North Point', 'Quarry Bay', 'Tai Koo', 'Shau Kei Wan'],
    states: ['Hong Kong Island', 'Hong Kong Island', 'Hong Kong Island', 'Kowloon', 'Kowloon', 'Kowloon', 'Kowloon', 'Hong Kong Island', 'Hong Kong Island', 'Hong Kong Island', 'Hong Kong Island', 'Hong Kong Island', 'Hong Kong Island', 'Hong Kong Island', 'Hong Kong Island'],
    districts: ['Central', 'Wan Chai', 'Causeway Bay', 'Tsim Sha Tsui', 'Mong Kok', 'Yau Ma Tei', 'Jordan', 'Admiralty', 'Sheung Wan', 'Mid-Levels', 'Happy Valley', 'North Point', 'Quarry Bay', 'Tai Koo', 'Shau Kei Wan'],
    streets: ['Des Voeux Road', 'Queen\'s Road', 'Nathan Road', 'Canton Road', 'Hennessy Road', 'Lockhart Road', 'Gloucester Road', 'Connaught Road', 'Pedder Street', 'Ice House Street', 'Wellington Street', 'Hollywood Road', 'Caine Road', 'Robinson Road', 'Kennedy Road'],
    postalCodes: ['', '', '', '', '', '', '', '', '', '', '', '', '', '', ''], // 香港不使用邮编
    format: 'standard'
  },

  // 新加坡 - 格式：姓名/街道/单位/建筑/新加坡/邮编/邮箱
  'en_SG': {
    cities: ['Singapore', 'Singapore', 'Singapore', 'Singapore', 'Singapore', 'Singapore', 'Singapore', 'Singapore', 'Singapore', 'Singapore', 'Singapore', 'Singapore', 'Singapore', 'Singapore', 'Singapore'],
    states: ['Central', 'Orchard', 'Marina Bay', 'Chinatown', 'Little India', 'Bugis', 'Clarke Quay', 'Raffles Place', 'Tanjong Pagar', 'Boat Quay', 'Robertson Quay', 'Dhoby Ghaut', 'Somerset', 'City Hall', 'Esplanade'],
    districts: ['Central', 'Orchard', 'Marina Bay', 'Chinatown', 'Little India', 'Bugis', 'Clarke Quay', 'Raffles Place', 'Tanjong Pagar', 'Boat Quay', 'Robertson Quay', 'Dhoby Ghaut', 'Somerset', 'City Hall', 'Esplanade'],
    streets: ['Orchard Road', 'Marina Bay', 'Raffles Avenue', 'Shenton Way', 'Robinson Road', 'Cecil Street', 'Collyer Quay', 'Battery Road', 'Boat Quay', 'Clarke Quay', 'Robertson Quay', 'North Bridge Road', 'South Bridge Road', 'New Bridge Road', 'Eu Tong Sen Street'],
    postalCodes: ['238872', '018956', '039594', '048623', '068809', '179104', '179024', '048584', '068804', '049909', '238872', '179103', '228208', '179555', '039594'],
    format: 'standard'
  },

  // 澳大利亚 - 格式：姓名/街道/城市/州/邮编/国家/邮箱
  'en_AU': {
    cities: ['Sydney', 'Melbourne', 'Brisbane', 'Perth', 'Adelaide', 'Canberra', 'Darwin', 'Hobart', 'Gold Coast', 'Newcastle', 'Wollongong', 'Geelong', 'Townsville', 'Cairns', 'Toowoomba'],
    states: ['NSW', 'VIC', 'QLD', 'WA', 'SA', 'ACT', 'NT', 'TAS', 'QLD', 'NSW', 'NSW', 'VIC', 'QLD', 'QLD', 'QLD'],
    districts: ['CBD', 'CBD', 'CBD', 'CBD', 'CBD', 'CBD', 'CBD', 'CBD', 'Surfers Paradise', 'CBD', 'CBD', 'CBD', 'CBD', 'CBD', 'CBD'],
    streets: ['George Street', 'Collins Street', 'Queen Street', 'St Georges Terrace', 'King William Street', 'Northbourne Avenue', 'Smith Street', 'Elizabeth Street', 'Cavill Avenue', 'Hunter Street', 'Crown Street', 'Moorabool Street', 'Flinders Street', 'Abbott Street', 'Margaret Street'],
    postalCodes: ['2000', '3000', '4000', '6000', '5000', '2600', '0800', '7000', '4217', '2300', '2500', '3220', '4810', '4870', '4350'],
    format: 'standard'
  },

  // 巴西 - 格式：姓名/街道/区/城市/州/邮编/国家/邮箱
  'pt_BR': {
    cities: ['São Paulo', 'Rio de Janeiro', 'Brasília', 'Salvador', 'Fortaleza', 'Belo Horizonte', 'Manaus', 'Curitiba', 'Recife', 'Goiânia', 'Belém', 'Porto Alegre', 'Guarulhos', 'Campinas', 'São Luís'],
    states: ['SP', 'RJ', 'DF', 'BA', 'CE', 'MG', 'AM', 'PR', 'PE', 'GO', 'PA', 'RS', 'SP', 'SP', 'MA'],
    districts: ['Centro', 'Copacabana', 'Asa Norte', 'Pelourinho', 'Centro', 'Centro', 'Centro', 'Centro', 'Boa Viagem', 'Centro', 'Centro', 'Centro Histórico', 'Centro', 'Centro', 'Centro'],
    streets: ['Rua Augusta', 'Avenida Atlântica', 'Esplanada dos Ministérios', 'Rua Chile', 'Avenida Beira Mar', 'Avenida Afonso Pena', 'Avenida Eduardo Ribeiro', 'Rua XV de Novembro', 'Avenida Boa Viagem', 'Avenida Goiás', 'Avenida Presidente Vargas', 'Rua dos Andradas', 'Avenida Santos Dumont', 'Avenida Francisco Glicério', 'Rua Grande'],
    postalCodes: ['01310-100', '22070-900', '70040-010', '40020-110', '60160-230', '30112-000', '69005-040', '80020-310', '51111-050', '74023-010', '66017-000', '90020-160', '07023-070', '13015-102', '65010-680'],
    format: 'standard'
  },

  // 菲律宾 - 格式：姓名/街道/区/城市/省/邮编/国家/邮箱
  'en_PH': {
    cities: ['Manila', 'Quezon City', 'Makati', 'Cebu City', 'Davao City', 'Pasig', 'Taguig', 'Antipolo', 'Zamboanga City', 'Cagayan de Oro', 'Paranaque', 'Dasmarinas', 'Valenzuela', 'Bacoor', 'General Santos'],
    states: ['Metro Manila', 'Metro Manila', 'Metro Manila', 'Cebu', 'Davao del Sur', 'Metro Manila', 'Metro Manila', 'Rizal', 'Zamboanga del Sur', 'Misamis Oriental', 'Metro Manila', 'Cavite', 'Metro Manila', 'Cavite', 'South Cotabato'],
    districts: ['Ermita', 'Diliman', 'Salcedo Village', 'Lahug', 'Poblacion', 'Ortigas Center', 'Bonifacio Global City', 'City Center', 'City Center', 'City Center', 'City Center', 'City Center', 'City Center', 'City Center', 'City Center'],
    streets: ['Rizal Avenue', 'Commonwealth Avenue', 'Ayala Avenue', 'Colon Street', 'Roxas Avenue', 'EDSA', 'McKinley Parkway', 'Circumferential Road', 'Mayor Jaldon Street', 'Corrales Avenue', 'Sucat Road', 'Governor Drive', 'MacArthur Highway', 'Aguinaldo Highway', 'Pioneer Avenue'],
    postalCodes: ['1000', '1100', '1200', '6000', '8000', '1600', '1630', '1870', '7000', '9000', '1700', '4114', '1440', '4102', '9500'],
    format: 'standard'
  },

  // 孟加拉国 - 格式：姓名/街道/区/城市/邮编/国家/邮箱
  'bn_BD': {
    cities: ['ঢাকা', 'চট্টগ্রাম', 'খুলনা', 'রাজশাহী', 'সিলেট', 'বরিশাল', 'রংপুর', 'ময়মনসিংহ', 'কুমিল্লা', 'নারায়ণগঞ্জ', 'গাজীপুর', 'টাঙ্গাইল', 'জামালপুর', 'কিশোরগঞ্জ', 'নেত্রকোণা'],
    states: ['ঢাকা বিভাগ', 'চট্টগ্রাম বিভাগ', 'খুলনা বিভাগ', 'রাজশাহী বিভাগ', 'সিলেট বিভাগ', 'বরিশাল বিভাগ', 'রংপুর বিভাগ', 'ময়মনসিংহ বিভাগ', 'চট্টগ্রাম বিভাগ', 'ঢাকা বিভাগ', 'ঢাকা বিভাগ', 'ঢাকা বিভাগ', 'ময়মনসিংহ বিভাগ', 'ঢাকা বিভাগ', 'ময়মনসিংহ বিভাগ'],
    districts: ['ধানমন্ডি', 'আগ্রাবাদ', 'খুলনা সদর', 'রাজশাহী সদর', 'সিলেট সদর', 'বরিশাল সদর', 'রংপুর সদর', 'ময়মনসিংহ সদর', 'কুমিল্লা সদর', 'নারায়ণগঞ্জ সদর', 'গাজীপুর সদর', 'টাঙ্গাইল সদর', 'জামালপুর সদর', 'কিশোরগঞ্জ সদর', 'নেত্রকোণা সদর'],
    streets: ['মিরপুর রোড', 'চট্টগ্রাম রোড', 'খান জাহান আলী রোড', 'সিটি বাইপাস', 'জিন্দাবাজার', 'সদর রোড', 'স্টেশন রোড', 'ব্রহ্মপুত্র রোড', 'ঢাকা রোড', 'ঢাকা-সিলেট হাইওয়ে', 'ঢাকা-ময়মনসিংহ হাইওয়ে', 'ঢাকা-টাঙ্গাইল হাইওয়ে', 'ময়মনসিংহ রোড', 'ঢাকা রোড', 'ময়মনসিংহ রোড'],
    postalCodes: ['1000', '4000', '9000', '6000', '3100', '8200', '5400', '2200', '3500', '1400', '1700', '1900', '2000', '2300', '2400'],
    format: 'standard'
  },

  // 俄罗斯 - 格式：邮编/城市/街道/国家/邮箱
  'ru_RU': {
    cities: ['Москва', 'Санкт-Петербург', 'Новосибирск', 'Екатеринбург', 'Казань', 'Нижний Новгород', 'Челябинск', 'Самара', 'Омск', 'Ростов-на-Дону', 'Уфа', 'Красноярск', 'Воронеж', 'Пермь', 'Волгоград'],
    states: ['Москва', 'Санкт-Петербург', 'Новосибирская область', 'Свердловская область', 'Республика Татарстан', 'Нижегородская область', 'Челябинская область', 'Самарская область', 'Омская область', 'Ростовская область', 'Республика Башкортостан', 'Красноярский край', 'Воронежская область', 'Пермский край', 'Волгоградская область'],
    districts: ['Центральный', 'Центральный', 'Центральный', 'Центральный', 'Вахитовский', 'Нижегородский', 'Центральный', 'Самарский', 'Центральный', 'Ленинский', 'Октябрьский', 'Центральный', 'Центральный', 'Индустриальный', 'Центральный'],
    streets: ['Тверская улица', 'Невский проспект', 'Красный проспект', 'улица Ленина', 'улица Баумана', 'Большая Покровская', 'улица Кирова', 'улица Молодогвардейская', 'улица Ленина', 'Большая Садовая', 'улица Ленина', 'проспект Мира', 'проспект Революции', 'улица Ленина', 'проспект Ленина'],
    postalCodes: ['101000', '190000', '630000', '620000', '420000', '603000', '454000', '443000', '644000', '344000', '450000', '660000', '394000', '614000', '400000'],
    format: 'standard'
  },

  // 意大利 - 格式：姓名/街道/邮编/城市/省/国家/邮箱
  'it_IT': {
    cities: ['Roma', 'Milano', 'Napoli', 'Torino', 'Palermo', 'Genova', 'Bologna', 'Firenze', 'Bari', 'Catania', 'Venezia', 'Verona', 'Messina', 'Padova', 'Trieste'],
    states: ['RM', 'MI', 'NA', 'TO', 'PA', 'GE', 'BO', 'FI', 'BA', 'CT', 'VE', 'VR', 'ME', 'PD', 'TS'],
    districts: ['Centro Storico', 'Centro Storico', 'Centro Storico', 'Centro', 'Centro Storico', 'Centro Storico', 'Centro Storico', 'Centro Storico', 'Murat', 'Centro', 'San Marco', 'Centro Storico', 'Centro', 'Centro', 'Centro'],
    streets: ['Via del Corso', 'Via Montenapoleone', 'Via Toledo', 'Via Roma', 'Via Maqueda', 'Via del Campo', 'Via Rizzoli', 'Via de\' Tornabuoni', 'Via Sparano', 'Via Etnea', 'Piazza San Marco', 'Via Mazzini', 'Via Garibaldi', 'Via Roma', 'Via San Nicolò'],
    postalCodes: ['00100', '20100', '80100', '10100', '90100', '16100', '40100', '50100', '70100', '95100', '30100', '37100', '98100', '35100', '34100'],
    format: 'standard'
  },

  // 西班牙 - 格式：姓名/街道/邮编/城市/省/国家/邮箱
  'es_ES': {
    cities: ['Madrid', 'Barcelona', 'Valencia', 'Sevilla', 'Zaragoza', 'Málaga', 'Murcia', 'Palma', 'Las Palmas', 'Bilbao', 'Alicante', 'Córdoba', 'Valladolid', 'Vigo', 'Gijón'],
    states: ['Madrid', 'Barcelona', 'Valencia', 'Sevilla', 'Zaragoza', 'Málaga', 'Murcia', 'Baleares', 'Las Palmas', 'Vizcaya', 'Alicante', 'Córdoba', 'Valladolid', 'Pontevedra', 'Asturias'],
    districts: ['Centro', 'Eixample', 'Ciutat Vella', 'Casco Antiguo', 'Centro', 'Centro', 'Centro', 'Centro', 'Vegueta', 'Casco Viejo', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro'],
    streets: ['Gran Vía', 'Passeig de Gràcia', 'Calle Colón', 'Calle Sierpes', 'Calle Alfonso I', 'Calle Larios', 'Gran Vía', 'Passeig des Born', 'Calle Triana', 'Gran Vía', 'Rambla Méndez Núñez', 'Cruz Conde', 'Calle Santiago', 'Calle Príncipe', 'Calle Corrida'],
    postalCodes: ['28001', '08001', '46001', '41001', '50001', '29001', '30001', '07001', '35001', '48001', '03001', '14001', '47001', '36201', '33201'],
    format: 'standard'
  },

  // 加拿大 - 格式：姓名/街道/城市/省/邮编/国家/邮箱
  'en_CA': {
    cities: ['Toronto', 'Montreal', 'Vancouver', 'Calgary', 'Edmonton', 'Ottawa', 'Winnipeg', 'Quebec City', 'Hamilton', 'Kitchener', 'London', 'Victoria', 'Halifax', 'Oshawa', 'Windsor'],
    states: ['ON', 'QC', 'BC', 'AB', 'AB', 'ON', 'MB', 'QC', 'ON', 'ON', 'ON', 'BC', 'NS', 'ON', 'ON'],
    districts: ['Downtown', 'Ville-Marie', 'Downtown', 'Downtown', 'Downtown', 'ByWard Market', 'Downtown', 'Old Quebec', 'Downtown', 'Downtown', 'Downtown', 'Inner Harbour', 'Downtown', 'Downtown', 'Downtown'],
    streets: ['Yonge Street', 'Rue Sainte-Catherine', 'Robson Street', 'Stephen Avenue', 'Jasper Avenue', 'Sparks Street', 'Portage Avenue', 'Grande Allée', 'King Street', 'King Street', 'Dundas Street', 'Government Street', 'Spring Garden Road', 'Simcoe Street', 'Ouellette Avenue'],
    postalCodes: ['M5H 2N2', 'H3A 0G4', 'V6Z 2E6', 'T2P 2M5', 'T5J 2B6', 'K1P 5Z4', 'R3C 0V8', 'G1R 2L3', 'L8P 4X4', 'N2G 4X1', 'N6A 1E8', 'V8W 1P6', 'B3J 3R7', 'L1H 7K4', 'N9A 1B2'],
    format: 'standard'
  },

  // 波兰 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'pl_PL': {
    cities: ['Warszawa', 'Kraków', 'Łódź', 'Wrocław', 'Poznań', 'Gdańsk', 'Szczecin', 'Bydgoszcz', 'Lublin', 'Katowice', 'Białystok', 'Gdynia', 'Częstochowa', 'Radom', 'Sosnowiec'],
    states: ['mazowieckie', 'małopolskie', 'łódzkie', 'dolnośląskie', 'wielkopolskie', 'pomorskie', 'zachodniopomorskie', 'kujawsko-pomorskie', 'lubelskie', 'śląskie', 'podlaskie', 'pomorskie', 'śląskie', 'mazowieckie', 'śląskie'],
    districts: ['Śródmieście', 'Stare Miasto', 'Śródmieście', 'Stare Miasto', 'Stare Miasto', 'Główne Miasto', 'Centrum', 'Śródmieście', 'Stare Miasto', 'Śródmieście', 'Centrum', 'Śródmieście', 'Śródmieście', 'Śródmieście', 'Centrum'],
    streets: ['Nowy Świat', 'Floriańska', 'Piotrkowska', 'Rynek', 'Stary Rynek', 'Długa', 'al. Niepodległości', 'Gdańska', 'Krakowskie Przedmieście', 'Mariacka', 'Lipowa', 'Świętojańska', 'Najświętszej Maryi Panny', 'Żeromskiego', 'Modrzejowska'],
    postalCodes: ['00-001', '31-001', '90-001', '50-001', '61-001', '80-001', '70-001', '85-001', '20-001', '40-001', '15-001', '81-001', '42-200', '26-600', '41-200'],
    format: 'standard'
  },

  // 荷兰 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'nl_NL': {
    cities: ['Amsterdam', 'Rotterdam', 'Den Haag', 'Utrecht', 'Eindhoven', 'Tilburg', 'Groningen', 'Almere', 'Breda', 'Nijmegen', 'Enschede', 'Haarlem', 'Arnhem', 'Zaanstad', 'Amersfoort'],
    states: ['Noord-Holland', 'Zuid-Holland', 'Zuid-Holland', 'Utrecht', 'Noord-Brabant', 'Noord-Brabant', 'Groningen', 'Flevoland', 'Noord-Brabant', 'Gelderland', 'Overijssel', 'Noord-Holland', 'Gelderland', 'Noord-Holland', 'Utrecht'],
    districts: ['Centrum', 'Centrum', 'Centrum', 'Binnenstad', 'Centrum', 'Centrum', 'Binnenstad', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum'],
    streets: ['Damrak', 'Coolsingel', 'Lange Voorhout', 'Oudegracht', 'Stratumseind', 'Heuvelstraat', 'Grote Markt', 'Belfort', 'Grote Markt', 'Grote Markt', 'Oude Markt', 'Grote Markt', 'Korenmarkt', 'Gedempte Gracht', 'Hof'],
    postalCodes: ['1012 JS', '3012 CA', '2514 EG', '3511 AL', '5611 EM', '5038 EA', '9712 HN', '1315 HR', '4811 XN', '6511 WG', '7511 GB', '2011 ML', '6811 DV', '1506 MD', '3811 BM'],
    format: 'standard'
  },

  // 新西兰 - 格式：姓名/街道/城市/邮编/国家/邮箱
  'en_NZ': {
    cities: ['Auckland', 'Wellington', 'Christchurch', 'Hamilton', 'Tauranga', 'Napier-Hastings', 'Dunedin', 'Palmerston North', 'Nelson', 'Rotorua', 'New Plymouth', 'Whangarei', 'Invercargill', 'Whanganui', 'Gisborne'],
    states: ['Auckland', 'Wellington', 'Canterbury', 'Waikato', 'Bay of Plenty', 'Hawke\'s Bay', 'Otago', 'Manawatu-Wanganui', 'Tasman', 'Bay of Plenty', 'Taranaki', 'Northland', 'Southland', 'Manawatu-Wanganui', 'Gisborne'],
    districts: ['Auckland Central', 'Wellington Central', 'Christchurch Central', 'Hamilton Central', 'Tauranga Central', 'Napier Central', 'Dunedin Central', 'Palmerston North Central', 'Nelson Central', 'Rotorua Central', 'New Plymouth Central', 'Whangarei Central', 'Invercargill Central', 'Whanganui Central', 'Gisborne Central'],
    streets: ['Queen Street', 'Lambton Quay', 'Colombo Street', 'Victoria Street', 'Cameron Road', 'Emerson Street', 'George Street', 'The Square', 'Trafalgar Street', 'Tutanekai Street', 'Devon Street', 'Cameron Street', 'Dee Street', 'Victoria Avenue', 'Gladstone Road'],
    postalCodes: ['1010', '6011', '8011', '3204', '3110', '4110', '9016', '4410', '7010', '3010', '4310', '0110', '9810', '4500', '4010'],
    format: 'standard'
  },

  // 尼泊尔 - 格式：姓名/街道/城市/邮编/国家/邮箱
  'ne_NP': {
    cities: ['काठमाडौं', 'पोखरा', 'ललितपुर', 'भक्तपुर', 'बिराटनगर', 'धरान', 'भैरहवा', 'बुटवल', 'नेपालगञ्ज', 'हेटौडा', 'धनगढी', 'इटहरी', 'जनकपुर', 'त्रिभुवन', 'गोरखा'],
    states: ['बागमती प्रदेश', 'गण्डकी प्रदेश', 'बागमती प्रदेश', 'बागमती प्रदेश', 'प्रदेश नं. १', 'प्रदेश नं. १', 'लुम्बिनी प्रदेश', 'लुम्बिनी प्रदेश', 'कर्णाली प्रदेश', 'बागमती प्रदेश', 'सुदूरपश्चिम प्रदेश', 'प्रदेश नं. १', 'मधेश प्रदेश', 'बागमती प्रदेश', 'गण्डकी प्रदेश'],
    districts: ['काठमाडौं', 'कास्की', 'ललितपुर', 'भक्तपुर', 'मोरङ', 'सुनसरी', 'रुपन्देही', 'रुपन्देही', 'बाँके', 'मकवानपुर', 'कैलाली', 'सुनसरी', 'धनुषा', 'काभ्रेपलाञ्चोक', 'गोरखा'],
    streets: ['न्यू रोड', 'लेकसाइड', 'जावलाखेल', 'दरबार स्क्वायर', 'मेन रोड', 'घण्टाघर', 'सिद्धार्थ हाइवे', 'गोल्डेन गेट', 'बाँके रोड', 'पुल्चोक', 'अत्तरिया रोड', 'धरान बजार', 'जनकपुर धाम', 'दुलिखेल', 'गोरखा बजार'],
    postalCodes: ['44600', '33700', '44700', '44800', '56613', '56705', '32907', '32907', '21900', '44107', '10900', '56705', '45600', '45200', '34000'],
    format: 'standard'
  },

  // 伊朗 - 格式：姓名/街道/城市/邮编/国家/邮箱
  'fa_IR': {
    cities: ['تهران', 'مشهد', 'اصفهان', 'کرج', 'تبریز', 'شیراز', 'قم', 'اهواز', 'کرمانشاه', 'ارومیه', 'رشت', 'زاهدان', 'همدان', 'کرمان', 'یزد'],
    states: ['تهران', 'خراسان رضوی', 'اصفهان', 'البرز', 'آذربایجان شرقی', 'فارس', 'قم', 'خوزستان', 'کرمانشاه', 'آذربایجان غربی', 'گیلان', 'سیستان و بلوچستان', 'همدان', 'کرمان', 'یزد'],
    districts: ['منطقه ۱', 'منطقه ۱', 'منطقه ۱', 'منطقه ۱', 'منطقه ۱', 'منطقه ۱', 'منطقه ۱', 'منطقه ۱', 'منطقه ۱', 'منطقه ۱', 'منطقه ۱', 'منطقه ۱', 'منطقه ۱', 'منطقه ۱', 'منطقه ۱'],
    streets: ['خیابان ولیعصر', 'خیابان امام رضا', 'خیابان چهارباغ', 'خیابان طالقانی', 'خیابان ولیعهد', 'خیابان زند', 'خیابان معلم', 'خیابان کیانپارس', 'خیابان جمهوری', 'خیابان امام خمینی', 'خیابان انزلی', 'خیابان دانشگاه', 'خیابان بوعلی', 'خیابان جمهوری', 'خیابان صفائیه'],
    postalCodes: ['1111111111', '9111111111', '8111111111', '3111111111', '5111111111', '7111111111', '3711111111', '6111111111', '6711111111', '5711111111', '4111111111', '9811111111', '6511111111', '7611111111', '8911111111'],
    format: 'standard'
  },

  // 以色列 - 格式：姓名/街道/城市/邮编/国家/邮箱
  'he_IL': {
    cities: ['ירושלים', 'תל אביב', 'חיפה', 'ראשון לציון', 'אשדוד', 'נתניה', 'באר שבע', 'בני ברק', 'חולון', 'רמת גן', 'אשקלון', 'רחובות', 'בת ים', 'כפר סבא', 'הרצליה'],
    states: ['ירושלים', 'תל אביב', 'חיפה', 'מרכז', 'דרום', 'מרכז', 'דרום', 'תל אביב', 'תל אביב', 'תל אביב', 'דרום', 'מרכז', 'תל אביב', 'מרכז', 'מרכז'],
    districts: ['מרכז העיר', 'מרכז העיר', 'הדר', 'מרכז העיר', 'מרכז העיר', 'מרכז העיר', 'מרכז העיר', 'מרכז העיר', 'מרכז העיר', 'מרכז העיר', 'מרכז העיר', 'מרכז העיר', 'מרכז העיר', 'מרכז העיר', 'מרכז העיר'],
    streets: ['רחוב יפו', 'רחוב דיזנגוף', 'שדרות בן גוריון', 'רחוב הרצל', 'רחוב ירושלים', 'רחוב הרצל', 'רחוב הרצל', 'רחוב רבי עקיבא', 'רחוב סוקולוב', 'רחוב ביאליק', 'רחוב הרצל', 'רחוב הרצל', 'רחוב בן גוריון', 'רחוב ויצמן', 'רחוב סוקולוב'],
    postalCodes: ['9111001', '6111001', '3111001', '7511001', '7711001', '4211001', '8411001', '5111001', '5811001', '5211001', '7811001', '7611001', '5911001', '4411001', '4611001'],
    format: 'standard'
  },

  // 土耳其 - 格式：姓名/街道/城市/邮编/国家/邮箱
  'tr_TR': {
    cities: ['İstanbul', 'Ankara', 'İzmir', 'Bursa', 'Antalya', 'Adana', 'Konya', 'Şanlıurfa', 'Gaziantep', 'Kayseri', 'Mersin', 'Eskişehir', 'Diyarbakır', 'Samsun', 'Denizli'],
    states: ['İstanbul', 'Ankara', 'İzmir', 'Bursa', 'Antalya', 'Adana', 'Konya', 'Şanlıurfa', 'Gaziantep', 'Kayseri', 'Mersin', 'Eskişehir', 'Diyarbakır', 'Samsun', 'Denizli'],
    districts: ['Fatih', 'Çankaya', 'Konak', 'Osmangazi', 'Muratpaşa', 'Seyhan', 'Selçuklu', 'Haliliye', 'Şahinbey', 'Melikgazi', 'Yenişehir', 'Odunpazarı', 'Sur', 'İlkadım', 'Pamukkale'],
    streets: ['İstiklal Caddesi', 'Kızılay', 'Kordon', 'Atatürk Caddesi', 'Kaleiçi', 'Ziyapaşa Bulvarı', 'Mevlana Caddesi', 'Şanlıurfa Caddesi', 'İstasyon Caddesi', 'Sivas Caddesi', 'İstiklal Caddesi', 'Porsuk Bulvarı', 'Gazi Caddesi', 'Atatürk Bulvarı', 'İstasyon Caddesi'],
    postalCodes: ['34110', '06100', '35220', '16040', '07100', '01120', '42060', '63300', '27010', '38010', '33110', '26040', '21100', '55030', '20160'],
    format: 'standard'
  },

  // 瑞典 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'sv_SE': {
    cities: ['Stockholm', 'Göteborg', 'Malmö', 'Uppsala', 'Västerås', 'Örebro', 'Linköping', 'Helsingborg', 'Jönköping', 'Norrköping', 'Lund', 'Umeå', 'Gävle', 'Borås', 'Eskilstuna'],
    states: ['Stockholm', 'Västra Götaland', 'Skåne', 'Uppsala', 'Västmanland', 'Örebro', 'Östergötland', 'Skåne', 'Jönköping', 'Östergötland', 'Skåne', 'Västerbotten', 'Gävleborg', 'Västra Götaland', 'Södermanland'],
    districts: ['Gamla Stan', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum'],
    streets: ['Drottninggatan', 'Avenyn', 'Stortorget', 'Sankt Eriks gata', 'Stora gatan', 'Drottninggatan', 'Stora torget', 'Kullagatan', 'Västra Storgatan', 'Drottninggatan', 'Stora Södergatan', 'Storgatan', 'Drottninggatan', 'Stora Brogatan', 'Fristadstorget'],
    postalCodes: ['111 29', '411 05', '211 22', '753 09', '722 12', '702 25', '582 19', '252 20', '553 21', '602 23', '222 21', '903 25', '801 23', '503 30', '631 86'],
    format: 'standard'
  },

  // 挪威 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'nb_NO': {
    cities: ['Oslo', 'Bergen', 'Trondheim', 'Stavanger', 'Bærum', 'Kristiansand', 'Fredrikstad', 'Tromsø', 'Sandnes', 'Drammen', 'Asker', 'Lillestrøm', 'Moss', 'Haugesund', 'Lørenskog'],
    states: ['Oslo', 'Vestland', 'Trøndelag', 'Rogaland', 'Viken', 'Agder', 'Viken', 'Troms og Finnmark', 'Rogaland', 'Viken', 'Viken', 'Viken', 'Viken', 'Rogaland', 'Viken'],
    districts: ['Sentrum', 'Sentrum', 'Sentrum', 'Sentrum', 'Sentrum', 'Sentrum', 'Sentrum', 'Sentrum', 'Sentrum', 'Sentrum', 'Sentrum', 'Sentrum', 'Sentrum', 'Sentrum', 'Sentrum'],
    streets: ['Karl Johans gate', 'Bryggen', 'Munkegata', 'Øvre Holmegate', 'Sandvika Storsenter', 'Markens gate', 'Storgata', 'Storgata', 'Langgata', 'Bragernes Torg', 'Asker Sentrum', 'Storgata', 'Fleischer gate', 'Haraldsgata', 'Lørenskog Sentrum'],
    postalCodes: ['0154', '5003', '7011', '4006', '1338', '4611', '1607', '9008', '4306', '3044', '1383', '2000', '1531', '5528', '1470'],
    format: 'standard'
  },

  // 丹麦 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'da_DK': {
    cities: ['København', 'Aarhus', 'Odense', 'Aalborg', 'Esbjerg', 'Randers', 'Kolding', 'Horsens', 'Vejle', 'Roskilde', 'Herning', 'Hørsholm', 'Silkeborg', 'Næstved', 'Fredericia'],
    states: ['Hovedstaden', 'Midtjylland', 'Syddanmark', 'Nordjylland', 'Syddanmark', 'Midtjylland', 'Syddanmark', 'Midtjylland', 'Syddanmark', 'Sjælland', 'Midtjylland', 'Hovedstaden', 'Midtjylland', 'Sjælland', 'Syddanmark'],
    districts: ['Indre By', 'Midtbyen', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum'],
    streets: ['Strøget', 'Ryesgade', 'Vestergade', 'Bispensgade', 'Kongensgade', 'Store Torv', 'Akseltorv', 'Søndergade', 'Sankt Nikolaj Gade', 'Skomagergade', 'Østergade', 'Rungsted Strandvej', 'Østergade', 'Sankt Peders Kirkeplads', 'Gothersgade'],
    postalCodes: ['1050', '8000', '5000', '9000', '6700', '8900', '6000', '8700', '7100', '4000', '7400', '2970', '8600', '4700', '7000'],
    format: 'standard'
  },

  // 芬兰 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'fi_FI': {
    cities: ['Helsinki', 'Espoo', 'Tampere', 'Vantaa', 'Oulu', 'Turku', 'Jyväskylä', 'Lahti', 'Kuopio', 'Pori', 'Joensuu', 'Lappeenranta', 'Hämeenlinna', 'Vaasa', 'Seinäjoki'],
    states: ['Uusimaa', 'Uusimaa', 'Pirkanmaa', 'Uusimaa', 'Pohjois-Pohjanmaa', 'Varsinais-Suomi', 'Keski-Suomi', 'Päijät-Häme', 'Pohjois-Savo', 'Satakunta', 'Pohjois-Karjala', 'Etelä-Karjala', 'Kanta-Häme', 'Pohjanmaa', 'Etelä-Pohjanmaa'],
    districts: ['Keskusta', 'Keskus', 'Keskusta', 'Keskus', 'Keskusta', 'Keskusta', 'Keskusta', 'Keskusta', 'Keskusta', 'Keskusta', 'Keskusta', 'Keskusta', 'Keskusta', 'Keskusta', 'Keskusta'],
    streets: ['Aleksanterinkatu', 'Tapiontori', 'Hämeenkatu', 'Kielotie', 'Kirkkokatu', 'Kauppatori', 'Kauppakatu', 'Aleksanterinkatu', 'Kauppakatu', 'Yrjönkatu', 'Joensuu', 'Kauppakatu', 'Raatihuoneenkatu', 'Hovioikeudenpuistikko', 'Kauppakatu'],
    postalCodes: ['00100', '02100', '33100', '01300', '90100', '20100', '40100', '15100', '70100', '28100', '80100', '53100', '13100', '65100', '60100'],
    format: 'standard'
  },

  // 冰岛 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'is_IS': {
    cities: ['Reykjavík', 'Kópavogur', 'Hafnarfjörður', 'Akureyri', 'Reykjanesbær', 'Garðabær', 'Mosfellsbær', 'Árborg', 'Akranes', 'Fjarðabyggð', 'Selfoss', 'Seltjarnarnes', 'Vestmannaeyjar', 'Grindavík', 'Ísafjörður'],
    states: ['Höfuðborgarsvæðið', 'Höfuðborgarsvæðið', 'Höfuðborgarsvæðið', 'Norðurland eystra', 'Suðurnes', 'Höfuðborgarsvæðið', 'Höfuðborgarsvæðið', 'Suðurland', 'Vesturland', 'Austurland', 'Suðurland', 'Höfuðborgarsvæðið', 'Suðurland', 'Suðurnes', 'Vestfirðir'],
    districts: ['Miðborg', 'Miðbær', 'Miðbær', 'Miðbær', 'Miðbær', 'Miðbær', 'Miðbær', 'Miðbær', 'Miðbær', 'Miðbær', 'Miðbær', 'Miðbær', 'Miðbær', 'Miðbær', 'Miðbær'],
    streets: ['Laugavegur', 'Hamraborg', 'Strandgata', 'Hafnarstræti', 'Tjarnargata', 'Garðatorg', 'Háaleitisbraut', 'Austurvegur', 'Kirkjutorg', 'Hafnargata', 'Austurvegur', 'Austurstræti', 'Vestmannabraut', 'Víkurbraut', 'Aðalstræti'],
    postalCodes: ['101', '200', '220', '600', '230', '210', '270', '800', '300', '700', '800', '170', '900', '240', '400'],
    format: 'standard'
  },

  // 奥地利 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'at_AT': {
    cities: ['Wien', 'Graz', 'Linz', 'Salzburg', 'Innsbruck', 'Klagenfurt', 'Villach', 'Wels', 'Sankt Pölten', 'Dornbirn', 'Steyr', 'Wiener Neustadt', 'Feldkirch', 'Bregenz', 'Leonding'],
    states: ['Wien', 'Steiermark', 'Oberösterreich', 'Salzburg', 'Tirol', 'Kärnten', 'Kärnten', 'Oberösterreich', 'Niederösterreich', 'Vorarlberg', 'Oberösterreich', 'Niederösterreich', 'Vorarlberg', 'Vorarlberg', 'Oberösterreich'],
    districts: ['Innere Stadt', 'Innere Stadt', 'Innenstadt', 'Altstadt', 'Altstadt', 'Innere Stadt', 'Innenstadt', 'Innenstadt', 'Innenstadt', 'Zentrum', 'Stadtplatz', 'Innenstadt', 'Innenstadt', 'Innenstadt', 'Zentrum'],
    streets: ['Graben', 'Herrengasse', 'Landstraße', 'Getreidegasse', 'Maria-Theresien-Straße', 'Alter Platz', 'Hauptplatz', 'Stadtplatz', 'Rathausplatz', 'Marktplatz', 'Stadtplatz', 'Hauptplatz', 'Marktgasse', 'Kornmarktplatz', 'Stadtplatz'],
    postalCodes: ['1010', '8010', '4020', '5020', '6020', '9020', '9500', '4600', '3100', '6850', '4400', '2700', '6800', '6900', '4060'],
    format: 'standard'
  },

  // 奥地利德语 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'de_AT': {
    cities: ['Wien', 'Graz', 'Linz', 'Salzburg', 'Innsbruck', 'Klagenfurt', 'Villach', 'Wels', 'Sankt Pölten', 'Dornbirn', 'Steyr', 'Wiener Neustadt', 'Feldkirch', 'Bregenz', 'Leonding'],
    states: ['Wien', 'Steiermark', 'Oberösterreich', 'Salzburg', 'Tirol', 'Kärnten', 'Kärnten', 'Oberösterreich', 'Niederösterreich', 'Vorarlberg', 'Oberösterreich', 'Niederösterreich', 'Vorarlberg', 'Vorarlberg', 'Oberösterreich'],
    districts: ['Innere Stadt', 'Innere Stadt', 'Innenstadt', 'Altstadt', 'Altstadt', 'Innere Stadt', 'Innenstadt', 'Innenstadt', 'Innenstadt', 'Zentrum', 'Stadtplatz', 'Innenstadt', 'Innenstadt', 'Innenstadt', 'Zentrum'],
    streets: ['Graben', 'Herrengasse', 'Landstraße', 'Getreidegasse', 'Maria-Theresien-Straße', 'Alter Platz', 'Hauptplatz', 'Stadtplatz', 'Rathausplatz', 'Marktplatz', 'Stadtplatz', 'Hauptplatz', 'Marktgasse', 'Kornmarktplatz', 'Stadtplatz'],
    postalCodes: ['1010', '8010', '4020', '5020', '6020', '9020', '9500', '4600', '3100', '6850', '4400', '2700', '6800', '6900', '4060'],
    format: 'standard'
  },

  // 瑞士德语 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'de_CH': {
    cities: ['Zürich', 'Basel', 'Bern', 'Winterthur', 'Luzern', 'Sankt Gallen', 'Lugano', 'Biel', 'Thun', 'Köniz', 'La Chaux-de-Fonds', 'Schaffhausen', 'Fribourg', 'Chur', 'Neuchâtel'],
    states: ['Zürich', 'Basel-Stadt', 'Bern', 'Zürich', 'Luzern', 'Sankt Gallen', 'Tessin', 'Bern', 'Bern', 'Bern', 'Neuchâtel', 'Schaffhausen', 'Fribourg', 'Graubünden', 'Neuchâtel'],
    districts: ['Altstadt', 'Altstadt', 'Altstadt', 'Altstadt', 'Altstadt', 'Altstadt', 'Centro', 'Altstadt', 'Altstadt', 'Zentrum', 'Centre', 'Altstadt', 'Centre', 'Altstadt', 'Centre'],
    streets: ['Bahnhofstrasse', 'Freie Strasse', 'Kramgasse', 'Marktgasse', 'Kapellplatz', 'Multergasse', 'Via Nassa', 'Nidaugasse', 'Hauptgasse', 'Schwarzenburgstrasse', 'Avenue Léopold-Robert', 'Vordergasse', 'Rue de Romont', 'Poststrasse', 'Rue du Seyon'],
    postalCodes: ['8001', '4001', '3011', '8400', '6003', '9001', '6900', '2502', '3600', '3098', '2300', '8200', '1700', '7001', '2000'],
    format: 'standard'
  },

  // 瑞士法语 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'fr_CH': {
    cities: ['Genève', 'Lausanne', 'Neuchâtel', 'Fribourg', 'Sion', 'La Chaux-de-Fonds', 'Yverdon-les-Bains', 'Montreux', 'Delémont', 'Martigny', 'Vevey', 'Nyon', 'Morges', 'Renens', 'Carouge'],
    states: ['Genève', 'Vaud', 'Neuchâtel', 'Fribourg', 'Valais', 'Neuchâtel', 'Vaud', 'Vaud', 'Jura', 'Valais', 'Vaud', 'Vaud', 'Vaud', 'Vaud', 'Genève'],
    districts: ['Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre'],
    streets: ['Rue du Rhône', 'Rue de Bourg', 'Rue du Seyon', 'Rue de Romont', 'Rue du Grand-Pont', 'Avenue Léopold-Robert', 'Rue de la Plaine', 'Grand-Rue', 'Rue de la Maltière', 'Avenue de la Gare', 'Rue du Lac', 'Rue de la Gare', 'Grand-Rue', 'Rue de Lausanne', 'Rue Saint-Joseph'],
    postalCodes: ['1201', '1003', '2000', '1700', '1950', '2300', '1400', '1820', '2800', '1920', '1800', '1260', '1110', '1020', '1227'],
    format: 'standard'
  },

  // 瑞士意大利语 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'it_CH': {
    cities: ['Lugano', 'Bellinzona', 'Locarno', 'Mendrisio', 'Chiasso', 'Ascona', 'Biasca', 'Airolo', 'Faido', 'Acquarossa', 'Giornico', 'Magadino', 'Tenero', 'Minusio', 'Muralto'],
    states: ['Ticino', 'Ticino', 'Ticino', 'Ticino', 'Ticino', 'Ticino', 'Ticino', 'Ticino', 'Ticino', 'Ticino', 'Ticino', 'Ticino', 'Ticino', 'Ticino', 'Ticino'],
    districts: ['Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro'],
    streets: ['Via Nassa', 'Piazza Collegiata', 'Piazza Grande', 'Via Lavizzari', 'Corso San Gottardo', 'Via Borgo', 'Via Centrale', 'Via Stazione', 'Via Cantonale', 'Via Cantonale', 'Via Cantonale', 'Via Cantonale', 'Via alle Brere', 'Via Verbano', 'Piazza Stazione'],
    postalCodes: ['6900', '6500', '6600', '6850', '6830', '6612', '6710', '6780', '6760', '6716', '6745', '6573', '6598', '6648', '6600'],
    format: 'standard'
  },

  // 比利时法语 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'fr_BE': {
    cities: ['Bruxelles', 'Liège', 'Charleroi', 'Namur', 'Mons', 'La Louvière', 'Tournai', 'Seraing', 'Verviers', 'Mouscron', 'Dinant', 'Arlon', 'Bastogne', 'Marche-en-Famenne', 'Rochefort'],
    states: ['Bruxelles-Capitale', 'Liège', 'Hainaut', 'Namur', 'Hainaut', 'Hainaut', 'Hainaut', 'Liège', 'Liège', 'Hainaut', 'Namur', 'Luxembourg', 'Luxembourg', 'Luxembourg', 'Namur'],
    districts: ['Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre', 'Centre'],
    streets: ['Rue Neuve', 'Rue Léopold', 'Boulevard Tirou', 'Rue de Fer', 'Rue de Nimy', 'Rue Sylvain Guyaux', 'Grand-Place', 'Rue du Pairay', 'Rue Xhavée', 'Rue de Courtrai', 'Rue Grande', 'Rue Paul Reuter', 'Place McAuliffe', 'Rue du Commerce', 'Rue de Behogne'],
    postalCodes: ['1000', '4000', '6000', '5000', '7000', '7100', '7500', '4100', '4800', '7700', '5500', '6700', '6600', '6900', '5430'],
    format: 'standard'
  },

  // 比利时荷兰语 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'nl_BE': {
    cities: ['Antwerpen', 'Gent', 'Brugge', 'Leuven', 'Mechelen', 'Aalst', 'Kortrijk', 'Hasselt', 'Sint-Niklaas', 'Oostende', 'Genk', 'Roeselare', 'Turnhout', 'Vilvoorde', 'Dendermonde'],
    states: ['Antwerpen', 'Oost-Vlaanderen', 'West-Vlaanderen', 'Vlaams-Brabant', 'Antwerpen', 'Oost-Vlaanderen', 'West-Vlaanderen', 'Limburg', 'Oost-Vlaanderen', 'West-Vlaanderen', 'Limburg', 'West-Vlaanderen', 'Antwerpen', 'Vlaams-Brabant', 'Oost-Vlaanderen'],
    districts: ['Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum'],
    streets: ['Meir', 'Veldstraat', 'Steenstraat', 'Bondgenotenlaan', 'Grote Markt', 'Grote Markt', 'Grote Markt', 'Grote Markt', 'Grote Markt', 'Kapellestraat', 'Grote Markt', 'Grote Markt', 'Grote Markt', 'Luchthavenlaan', 'Grote Markt'],
    postalCodes: ['2000', '9000', '8000', '3000', '2800', '9300', '8500', '3500', '9100', '8400', '3600', '8800', '2300', '1800', '9200'],
    format: 'standard'
  },

  // 葡萄牙 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'pt_PT': {
    cities: ['Lisboa', 'Porto', 'Vila Nova de Gaia', 'Amadora', 'Braga', 'Funchal', 'Coimbra', 'Setúbal', 'Almada', 'Agualva-Cacém', 'Queluz', 'Barreiro', 'Évora', 'Rio Tinto', 'Corroios'],
    states: ['Lisboa', 'Porto', 'Porto', 'Lisboa', 'Braga', 'Madeira', 'Coimbra', 'Setúbal', 'Setúbal', 'Lisboa', 'Lisboa', 'Setúbal', 'Évora', 'Porto', 'Setúbal'],
    districts: ['Baixa', 'Baixa', 'Centro', 'Centro', 'Centro', 'Centro', 'Baixa', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro'],
    streets: ['Rua Augusta', 'Rua de Santa Catarina', 'Rua da República', 'Rua Elias Garcia', 'Rua do Souto', 'Avenida Arriaga', 'Rua Ferreira Borges', 'Avenida Luísa Todi', 'Rua Capitão Leitão', 'Rua Principal', 'Rua Almirante Reis', 'Rua Miguel Bombarda', 'Rua 5 de Outubro', 'Rua da Igreja', 'Rua Principal'],
    postalCodes: ['1100-048', '4000-322', '4400-346', '2700-036', '4710-229', '9000-018', '3000-213', '2900-474', '2800-155', '2735-500', '2745-191', '2830-355', '7000-811', '4435-718', '2855-661'],
    format: 'standard'
  },

  // 希腊 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'el_GR': {
    cities: ['Αθήνα', 'Θεσσαλονίκη', 'Πάτρα', 'Ηράκλειο', 'Λάρισα', 'Βόλος', 'Ιωάννινα', 'Καβάλα', 'Σέρρες', 'Χανιά', 'Αλεξανδρούπολη', 'Ξάνθη', 'Κατερίνη', 'Αγρίνιο', 'Καλαμάτα'],
    states: ['Αττική', 'Κεντρική Μακεδονία', 'Δυτική Ελλάδα', 'Κρήτη', 'Θεσσαλία', 'Θεσσαλία', 'Ήπειρος', 'Ανατολική Μακεδονία και Θράκη', 'Κεντρική Μακεδονία', 'Κρήτη', 'Ανατολική Μακεδονία και Θράκη', 'Ανατολική Μακεδονία και Θράκη', 'Κεντρική Μακεδονία', 'Δυτική Ελλάδα', 'Πελοπόννησος'],
    districts: ['Κέντρο', 'Κέντρο', 'Κέντρο', 'Κέντρο', 'Κέντρο', 'Κέντρο', 'Κέντρο', 'Κέντρο', 'Κέντρο', 'Κέντρο', 'Κέντρο', 'Κέντρο', 'Κέντρο', 'Κέντρο', 'Κέντρο'],
    streets: ['Οδός Ερμού', 'Οδός Τσιμισκή', 'Οδός Ρήγα Φεραίου', 'Οδός 25ης Αυγούστου', 'Οδός Κύπρου', 'Οδός Δημητριάδος', 'Οδός Δωδώνης', 'Οδός Ερυθρού Σταυρού', 'Οδός Βενιζέλου', 'Οδός Χαλιδών', 'Οδός Δημοκρατίας', 'Οδός 28ης Οκτωβρίου', 'Οδός Μεγάλου Αλεξάνδρου', 'Οδός Παπαστράτου', 'Οδός Αριστομένους'],
    postalCodes: ['10431', '54622', '26221', '71202', '41222', '38221', '45444', '65403', '62122', '73100', '68100', '67100', '60100', '30100', '24100'],
    format: 'standard'
  },

  // 塞浦路斯希腊语 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'el_CY': {
    cities: ['Λευκωσία', 'Λεμεσός', 'Λάρνακα', 'Πάφος', 'Αμμόχωστος', 'Στρόβολος', 'Αγλαντζιά', 'Λακατάμια', 'Παραλίμνι', 'Πρωταράς', 'Αγία Νάπα', 'Πολεμίδια', 'Γέρι', 'Δάλι', 'Αραδίππου'],
    states: ['Λευκωσία', 'Λεμεσός', 'Λάρνακα', 'Πάφος', 'Αμμόχωστος', 'Λευκωσία', 'Λευκωσία', 'Λευκωσία', 'Αμμόχωστος', 'Αμμόχωστος', 'Αμμόχωστος', 'Λεμεσός', 'Λευκωσία', 'Λευκωσία', 'Λάρνακα'],
    districts: ['Κέντρο', 'Κέντρο', 'Κέντρο', 'Κέντρο', 'Κέντρο', 'Κέντρο', 'Κέντρο', 'Κέντρο', 'Κέντρο', 'Κέντρο', 'Κέντρο', 'Κέντρο', 'Κέντρο', 'Κέντρο', 'Κέντρο'],
    streets: ['Οδός Λήδρας', 'Οδός Αγίου Ανδρέα', 'Οδός Ζήνωνος Κιτιέως', 'Οδός Μακαρίου Γ\'', 'Οδός Αμμοχώστου', 'Οδός Στροβόλου', 'Οδός Αγλαντζιάς', 'Οδός Λακατάμιας', 'Οδός Παραλιμνίου', 'Οδός Πρωταρά', 'Οδός Αγίας Νάπας', 'Οδός Πολεμιδιών', 'Οδός Γερίου', 'Οδός Δαλίου', 'Οδός Αραδίππου'],
    postalCodes: ['1016', '3036', '6023', '8047', '5330', '2018', '2120', '2324', '5296', '5296', '5330', '4108', '2200', '2540', '7101'],
    format: 'standard'
  },

  // 捷克 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'cs_CZ': {
    cities: ['Praha', 'Brno', 'Ostrava', 'Plzeň', 'Liberec', 'Olomouc', 'Ústí nad Labem', 'České Budějovice', 'Hradec Králové', 'Pardubice', 'Zlín', 'Havířov', 'Kladno', 'Most', 'Opava'],
    states: ['Praha', 'Jihomoravský', 'Moravskoslezský', 'Plzeňský', 'Liberecký', 'Olomoucký', 'Ústecký', 'Jihočeský', 'Královéhradecký', 'Pardubický', 'Zlínský', 'Moravskoslezský', 'Středočeský', 'Ústecký', 'Moravskoslezský'],
    districts: ['Staré Město', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum'],
    streets: ['Václavské náměstí', 'Česká', 'Stodolní', 'náměstí Republiky', 'Soukenné náměstí', 'Horní náměstí', 'Mírové náměstí', 'náměstí Přemysla Otakara II.', 'Velké náměstí', 'náměstí Republiky', 'náměstí Míru', 'náměstí Republiky', 'náměstí Svobody', 'Radniční', 'Horní náměstí'],
    postalCodes: ['110 00', '602 00', '702 00', '301 00', '460 01', '779 00', '400 01', '370 01', '500 02', '530 02', '760 01', '736 01', '272 01', '434 01', '746 01'],
    format: 'standard'
  },

  // 斯洛伐克 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'sk_SK': {
    cities: ['Bratislava', 'Košice', 'Prešov', 'Žilina', 'Banská Bystrica', 'Nitra', 'Trnava', 'Martin', 'Trenčín', 'Poprad', 'Prievidza', 'Zvolen', 'Považská Bystrica', 'Michalovce', 'Spišská Nová Ves'],
    states: ['Bratislavský', 'Košický', 'Prešovský', 'Žilinský', 'Banskobystrický', 'Nitriansky', 'Trnavský', 'Žilinský', 'Trenčiansky', 'Prešovský', 'Trenčiansky', 'Banskobystrický', 'Trenčiansky', 'Košický', 'Košický'],
    districts: ['Staré Mesto', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum', 'Centrum'],
    streets: ['Hlavné námestie', 'Hlavná', 'Hlavná', 'Mariánske námestie', 'Námestie SNP', 'Svätoplukovo námestie', 'Trojičné námestie', 'Námestie SNP', 'Mierové námestie', 'Námestie sv. Egídia', 'Námestie slobody', 'Námestie SNP', 'Námestie slobody', 'Námestie osloboditeľov', 'Radničné námestie'],
    postalCodes: ['811 01', '040 01', '080 01', '010 01', '974 01', '949 01', '917 01', '036 01', '911 01', '058 01', '971 01', '960 01', '017 01', '071 01', '052 01'],
    format: 'standard'
  },

  // 匈牙利 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'hu_HU': {
    cities: ['Budapest', 'Debrecen', 'Szeged', 'Miskolc', 'Pécs', 'Győr', 'Nyíregyháza', 'Kecskemét', 'Székesfehérvár', 'Szombathely', 'Érd', 'Tatabánya', 'Kaposvár', 'Békéscsaba', 'Zalaegerszeg'],
    states: ['Budapest', 'Hajdú-Bihar', 'Csongrád-Csanád', 'Borsod-Abaúj-Zemplén', 'Baranya', 'Győr-Moson-Sopron', 'Szabolcs-Szatmár-Bereg', 'Bács-Kiskun', 'Fejér', 'Vas', 'Pest', 'Komárom-Esztergom', 'Somogy', 'Békés', 'Zala'],
    districts: ['Belváros', 'Centrum', 'Belváros', 'Belváros', 'Belváros', 'Belváros', 'Belváros', 'Belváros', 'Belváros', 'Belváros', 'Centrum', 'Centrum', 'Belváros', 'Centrum', 'Belváros'],
    streets: ['Váci utca', 'Piac utca', 'Kárász utca', 'Széchenyi utca', 'Király utca', 'Baross Gábor út', 'Kossuth tér', 'Kossuth tér', 'Városház tér', 'Fő tér', 'Budai út', 'Fő tér', 'Fő utca', 'Andrássy út', 'Széchenyi tér'],
    postalCodes: ['1052', '4025', '6720', '3525', '7621', '9021', '4400', '6000', '8000', '9700', '2030', '2800', '7400', '5600', '8900'],
    format: 'standard'
  },

  // 罗马尼亚 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'ro_RO': {
    cities: ['București', 'Cluj-Napoca', 'Timișoara', 'Iași', 'Constanța', 'Craiova', 'Brașov', 'Galați', 'Ploiești', 'Oradea', 'Braila', 'Arad', 'Pitești', 'Sibiu', 'Bacău'],
    states: ['București', 'Cluj', 'Timiș', 'Iași', 'Constanța', 'Dolj', 'Brașov', 'Galați', 'Prahova', 'Bihor', 'Brăila', 'Arad', 'Argeș', 'Sibiu', 'Bacău'],
    districts: ['Centrul Vechi', 'Centru', 'Centru', 'Centru', 'Centru', 'Centru', 'Centrul Istoric', 'Centru', 'Centru', 'Centru', 'Centru', 'Centru', 'Centru', 'Centrul Istoric', 'Centru'],
    streets: ['Calea Victoriei', 'Strada Memorandumului', 'Bulevardul Revoluției', 'Strada Lăpușneanu', 'Bulevardul Tomis', 'Calea Unirii', 'Strada Republicii', 'Strada Domnească', 'Bulevardul Republicii', 'Strada Republicii', 'Strada Republicii', 'Bulevardul Revoluției', 'Strada Victoriei', 'Strada Nicolae Bălcescu', 'Strada 9 Mai'],
    postalCodes: ['010095', '400001', '300001', '700259', '900178', '200585', '500025', '800003', '100001', '410058', '810001', '310001', '110014', '550024', '600001'],
    format: 'standard'
  },

  // 保加利亚 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'bg_BG': {
    cities: ['София', 'Пловдив', 'Варна', 'Бургас', 'Русе', 'Стара Загора', 'Плевен', 'Сливен', 'Добрич', 'Шумен', 'Перник', 'Хасково', 'Ямбол', 'Пазарджик', 'Благоевград'],
    states: ['София-град', 'Пловдив', 'Варна', 'Бургас', 'Русе', 'Стара Загора', 'Плевен', 'Сливен', 'Добрич', 'Шумен', 'Перник', 'Хасково', 'Ямбол', 'Пазарджик', 'Благоевград'],
    districts: ['Център', 'Център', 'Център', 'Център', 'Център', 'Център', 'Център', 'Център', 'Център', 'Център', 'Център', 'Център', 'Център', 'Център', 'Център'],
    streets: ['бул. Витоша', 'ул. Главна', 'бул. Княз Борис I', 'ул. Богориди', 'ул. Александровска', 'бул. Руски', 'ул. Васил Левски', 'бул. Хаджи Димитър', 'ул. 25 септември', 'бул. Славянски', 'ул. Кракра', 'бул. България', 'ул. Търговска', 'ул. Хан Крум', 'ул. Македония'],
    postalCodes: ['1000', '4000', '9000', '8000', '7000', '6000', '5800', '8800', '9300', '9700', '2300', '6300', '8600', '4400', '2700'],
    format: 'standard'
  },

  // 克罗地亚 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'hr_HR': {
    cities: ['Zagreb', 'Split', 'Rijeka', 'Osijek', 'Zadar', 'Slavonski Brod', 'Pula', 'Sesvete', 'Karlovac', 'Varaždin', 'Šibenik', 'Sisak', 'Vinkovci', 'Vukovar', 'Dubrovnik'],
    states: ['Zagreb', 'Split-Dalmatia', 'Primorje-Gorski Kotar', 'Osijek-Baranja', 'Zadar', 'Brod-Posavina', 'Istria', 'Zagreb', 'Karlovac', 'Varaždin', 'Šibenik-Knin', 'Sisak-Moslavina', 'Vukovar-Syrmia', 'Vukovar-Syrmia', 'Dubrovnik-Neretva'],
    districts: ['Centar', 'Centar', 'Centar', 'Centar', 'Centar', 'Centar', 'Centar', 'Centar', 'Centar', 'Centar', 'Centar', 'Centar', 'Centar', 'Centar', 'Centar'],
    streets: ['Ilica', 'Riva', 'Korzo', 'Europska avenija', 'Kalelarga', 'Ante Starčevića', 'Forum', 'Zagrebačka cesta', 'Banija', 'Franjevački trg', 'Zagrebačka', 'Rimska', 'Josipa Jelačića', 'Strossmayerova', 'Stradun'],
    postalCodes: ['10000', '21000', '51000', '31000', '23000', '35000', '52100', '10360', '47000', '42000', '22000', '44000', '32100', '32000', '20000'],
    format: 'standard'
  },

  // 塞尔维亚 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'sr_RS': {
    cities: ['Београд', 'Нови Сад', 'Ниш', 'Крагујевац', 'Суботица', 'Зрењанин', 'Панчево', 'Чачак', 'Нови Пазар', 'Кикинда', 'Сомбор', 'Ужице', 'Смедерево', 'Јагодина', 'Врање'],
    states: ['Београд', 'Војводина', 'Јужна Србија', 'Шумадија', 'Војводина', 'Војводина', 'Војводина', 'Западна Србија', 'Рашка', 'Војводина', 'Војводина', 'Западна Србија', 'Подунавље', 'Поморавље', 'Јужна Србија'],
    districts: ['Стари град', 'Центар', 'Центар', 'Центар', 'Центар', 'Центар', 'Центар', 'Центар', 'Центар', 'Центар', 'Центар', 'Центар', 'Центар', 'Центар', 'Центар'],
    streets: ['Кнез Михаилова', 'Змај Јовина', 'Обреновићева', 'Светозара Марковића', 'Корзо', 'Краљ Петар I', 'Војводе Радомира Путника', 'Градски трг', 'Рашка', 'Трг српских добровољаца', 'Трг Републике', 'Трг партизана', 'Карађорђева', 'Кнегиње Милице', 'Маршала Тита'],
    postalCodes: ['11000', '21000', '18000', '34000', '24000', '23000', '26000', '32000', '36300', '23300', '25000', '31000', '11300', '35000', '17500'],
    format: 'standard'
  },

  // 斯洛文尼亚 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'sl_SI': {
    cities: ['Ljubljana', 'Maribor', 'Celje', 'Kranj', 'Velenje', 'Koper', 'Novo Mesto', 'Ptuj', 'Trbovlje', 'Kamnik', 'Jesenice', 'Nova Gorica', 'Domžale', 'Škofja Loka', 'Murska Sobota'],
    states: ['Osrednjeslovenska', 'Podravska', 'Savinjska', 'Gorenjska', 'Savinjska', 'Obalno-kraška', 'Jugovzhodna Slovenija', 'Podravska', 'Zasavska', 'Gorenjska', 'Gorenjska', 'Goriška', 'Osrednjeslovenska', 'Gorenjska', 'Pomurska'],
    districts: ['Center', 'Center', 'Center', 'Center', 'Center', 'Center', 'Center', 'Center', 'Center', 'Center', 'Center', 'Center', 'Center', 'Center', 'Center'],
    streets: ['Čopova ulica', 'Glavna ulica', 'Krekov trg', 'Prešernov trg', 'Titov trg', 'Titov trg', 'Glavni trg', 'Slovenski trg', 'Trg svobode', 'Glavni trg', 'Cesta železarjev', 'Bevkov trg', 'Ljubljanska cesta', 'Mestni trg', 'Slovenska ulica'],
    postalCodes: ['1000', '2000', '3000', '4000', '3320', '6000', '8000', '2250', '1420', '1241', '4270', '5000', '1230', '4220', '9000'],
    format: 'standard'
  },

  // 北马其顿 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'mk_MK': {
    cities: ['Скопје', 'Битола', 'Куманово', 'Прилеп', 'Тетово', 'Велес', 'Штип', 'Охрид', 'Гостивар', 'Струмица', 'Радовиш', 'Дебар', 'Кавадарци', 'Гевгелија', 'Кичево'],
    states: ['Скопски', 'Пелагониски', 'Североисточен', 'Пелагониски', 'Полошки', 'Вардарски', 'Источен', 'Југозападен', 'Полошки', 'Југоисточен', 'Источен', 'Југозападен', 'Вардарски', 'Југоисточен', 'Југозападен'],
    districts: ['Центар', 'Центар', 'Центар', 'Центар', 'Центар', 'Центар', 'Центар', 'Центар', 'Центар', 'Центар', 'Центар', 'Центар', 'Центар', 'Центар', 'Центар'],
    streets: ['Македонија', 'Широк Сокак', 'Маршал Тито', 'Александар Македонски', 'Илинденска', 'Маршал Тито', 'Маршал Тито', 'Туристичка', 'Илинденска', 'Маршал Тито', 'Гоце Делчев', 'Илинденска', 'Маршал Тито', 'Маршал Тито', 'Илинденска'],
    postalCodes: ['1000', '7000', '1300', '7500', '1200', '1400', '2000', '6000', '1230', '2400', '2420', '1250', '1430', '1480', '6250'],
    format: 'standard'
  },

  // 阿尔巴尼亚 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'sq_AL': {
    cities: ['Tiranë', 'Durrës', 'Vlorë', 'Elbasan', 'Shkodër', 'Fier', 'Korçë', 'Berat', 'Lushnjë', 'Kavajë', 'Pogradec', 'Gjirokastër', 'Patos', 'Laç', 'Kukës'],
    states: ['Tiranë', 'Durrës', 'Vlorë', 'Elbasan', 'Shkodër', 'Fier', 'Korçë', 'Berat', 'Fier', 'Durrës', 'Korçë', 'Gjirokastër', 'Fier', 'Lezhë', 'Kukës'],
    districts: ['Qendër', 'Qendër', 'Qendër', 'Qendër', 'Qendër', 'Qendër', 'Qendër', 'Qendër', 'Qendër', 'Qendër', 'Qendër', 'Qendër', 'Qendër', 'Qendër', 'Qendër'],
    streets: ['Bulevardi Dëshmorët e Kombit', 'Rruga Durrësit', 'Bulevardi Ismail Qemali', 'Rruga Elbasan', 'Rruga Kol Idromeno', 'Rruga Apollonia', 'Bulevardi Republika', 'Rruga Antipatrea', 'Rruga Divjakë', 'Rruga Durrësit', 'Rruga Pogradec', 'Rruga Gjirokastra', 'Rruga Patos', 'Rruga Laç', 'Rruga Bajram Curri'],
    postalCodes: ['1001', '2001', '9401', '3001', '4001', '9301', '7001', '5001', '9101', '2501', '7301', '6001', '9201', '4501', '8501'],
    format: 'standard'
  },

  // 沙特阿拉伯 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'ar_SA': {
    cities: ['الرياض', 'جدة', 'مكة المكرمة', 'المدينة المنورة', 'الدمام', 'الخبر', 'تبوك', 'بريدة', 'خميس مشيط', 'حائل', 'نجران', 'الجبيل', 'ينبع', 'الطائف', 'القطيف'],
    states: ['الرياض', 'مكة المكرمة', 'مكة المكرمة', 'المدينة المنورة', 'المنطقة الشرقية', 'المنطقة الشرقية', 'تبوك', 'القصيم', 'عسير', 'حائل', 'نجران', 'المنطقة الشرقية', 'المدينة المنورة', 'مكة المكرمة', 'المنطقة الشرقية'],
    districts: ['الوسط', 'الوسط', 'الوسط', 'الوسط', 'الوسط', 'الوسط', 'الوسط', 'الوسط', 'الوسط', 'الوسط', 'الوسط', 'الوسط', 'الوسط', 'الوسط', 'الوسط'],
    streets: ['شارع الملك فهد', 'شارع الأمير محمد بن عبد العزيز', 'شارع إبراهيم الخليل', 'شارع الملك عبد العزيز', 'شارع الملك سعود', 'شارع الملك فيصل', 'شارع الأمير فهد بن سلطان', 'شارع الملك عبد العزيز', 'شارع الملك فهد', 'شارع الملك عبد العزيز', 'شارع الملك عبد العزيز', 'شارع الملك فهد', 'شارع الملك عبد العزيز', 'شارع الملك فهد', 'شارع الملك سعود'],
    postalCodes: ['11564', '21589', '24231', '42311', '31952', '31952', '47512', '51431', '61961', '55425', '66262', '35718', '46421', '26513', '32611'],
    format: 'standard'
  },

  // 巴基斯坦乌尔都语 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'ur_PK': {
    cities: ['کراچی', 'لاہور', 'فیصل آباد', 'راولپنڈی', 'ملتان', 'حیدرآباد', 'گجرانوالہ', 'پشاور', 'کوئٹہ', 'اسلام آباد', 'بہاولپور', 'سرگودھا', 'سیالکوٹ', 'سکھر', 'شیخوپورہ'],
    states: ['سندھ', 'پنجاب', 'پنجاب', 'پنجاب', 'پنجاب', 'سندھ', 'پنجاب', 'خیبر پختونخوا', 'بلوچستان', 'اسلام آباد', 'پنجاب', 'پنجاب', 'پنجاب', 'سندھ', 'پنجاب'],
    districts: ['مرکز', 'مرکز', 'مرکز', 'مرکز', 'مرکز', 'مرکز', 'مرکز', 'مرکز', 'مرکز', 'مرکز', 'مرکز', 'مرکز', 'مرکز', 'مرکز', 'مرکز'],
    streets: ['شاہراہ فیصل', 'مال روڈ', 'جناح روڈ', 'مری روڈ', 'نشتر روڈ', 'شاہراہ قائداعظم', 'جی ٹی روڈ', 'یونیورسٹی روڈ', 'شاہراہ قائداعظم', 'جناح ایونیو', 'شاہراہ قائداعظم', 'فیصل آباد روڈ', 'پیرس روڈ', 'شاہراہ قائداعظم', 'شیخوپورہ روڈ'],
    postalCodes: ['75500', '54000', '38000', '46000', '60000', '71000', '52250', '25000', '87300', '44000', '63100', '40100', '51310', '65200', '39350'],
    format: 'standard'
  },

  // 印度印地语 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'hi_IN': {
    cities: ['नई दिल्ली', 'मुंबई', 'कोलकाता', 'चेन्नई', 'बेंगलुरु', 'हैदराबाद', 'अहमदाबाद', 'पुणे', 'सूरत', 'जयपुर', 'लखनऊ', 'कानपुर', 'नागपुर', 'इंदौर', 'ठाणे'],
    states: ['दिल्ली', 'महाराष्ट्र', 'पश्चिम बंगाल', 'तमिलनाडु', 'कर्नाटक', 'तेलंगाना', 'गुजरात', 'महाराष्ट्र', 'गुजरात', 'राजस्थान', 'उत्तर प्रदेश', 'उत्तर प्रदेश', 'महाराष्ट्र', 'मध्य प्रदेश', 'महाराष्ट्र'],
    districts: ['केंद्र', 'केंद्र', 'केंद्र', 'केंद्र', 'केंद्र', 'केंद्र', 'केंद्र', 'केंद्र', 'केंद्र', 'केंद्र', 'केंद्र', 'केंद्र', 'केंद्र', 'केंद्र', 'केंद्र'],
    streets: ['कनॉट प्लेस', 'मरीन ड्राइव', 'पार्क स्ट्रीट', 'मरीना बीच रोड', 'एमजी रोड', 'हाई-टेक सिटी', 'सी.जी. रोड', 'एफसी रोड', 'रिंग रोड', 'एमआई रोड', 'हजरतगंज', 'मॉल रोड', 'सिविल लाइन्स', 'एमजी रोड', 'गोखले रोड'],
    postalCodes: ['110001', '400001', '700001', '600001', '560001', '500001', '380001', '411001', '395001', '302001', '226001', '208001', '440001', '452001', '400601'],
    format: 'standard'
  },

  // 印度泰米尔语 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'ta_IN': {
    cities: ['சென்னை', 'கோயம்புத்தூர்', 'மதுரை', 'திருச்சிராப்பள்ளி', 'சேலம்', 'திருநெல்வேலி', 'எரோடு', 'வேலூர்', 'தூத்துக்குடி', 'திருப்பூர்', 'ராணிப்பேட்டை', 'நாகர்கோயில்', 'தஞ்சாவூர்', 'டிண்டுக்கல்', 'கரூர்'],
    states: ['தமிழ்நாடு', 'தமிழ்நாடு', 'தமிழ்நாடு', 'தமிழ்நாடு', 'தமிழ்நாடு', 'தமிழ்நாடு', 'தமிழ்நாடு', 'தமிழ்நாடு', 'தமிழ்நாடு', 'தமிழ்நாடு', 'தமிழ்நாடு', 'தமிழ்நாடு', 'தமிழ்நாடு', 'தமிழ்நாடு', 'தமிழ்நாடு'],
    districts: ['மையம்', 'மையம்', 'மையம்', 'மையம்', 'மையம்', 'மையம்', 'மையம்', 'மையம்', 'மையம்', 'மையம்', 'மையம்', 'மையம்', 'மையம்', 'மையம்', 'மையம்'],
    streets: ['அண்ணா சாலை', 'கங்கை கொண்டான் தெரு', 'மீனாட்சி அம்மன் கோயில் தெரு', 'பிரிகேடியர் ராஜேந்திர சிங் சாலை', 'சேலம் மெயின் சாலை', 'திருநெல்வேலி ஜங்ஷன்', 'பெரியார் சாலை', 'கத்பாடி சாலை', 'பீச் சாலை', 'அவினாசி சாலை', 'ராணிப்பேட்டை மெயின் சாலை', 'கோட்டார் சாலை', 'கிராண்ட் அனைக்கட் சாலை', 'மதுரை சாலை', 'திருச்சி சாலை'],
    postalCodes: ['600001', '641001', '625001', '620001', '636001', '627001', '638001', '632001', '628001', '641601', '632401', '629001', '613001', '624001', '639001'],
    format: 'standard'
  },

  // 印度泰卢固语 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'te_IN': {
    cities: ['హైదరాబాద్', 'విజయవాడ', 'విశాఖపట్నం', 'గుంటూరు', 'నెల్లూరు', 'కర్నూలు', 'రాజమండ్రి', 'తిరుపతి', 'కడప', 'నిజామాబాద్', 'ఖమ్మం', 'వరంగల్', 'మహబూబ్‌నగర్', 'అనంతపురం', 'చిత్తూరు'],
    states: ['తెలంగాణ', 'ఆంధ్రప్రదేశ్', 'ఆంధ్రప్రదేశ్', 'ఆంధ్రప్రదేశ్', 'ఆంధ్రప్రదేశ్', 'ఆంధ్రప్రదేశ్', 'ఆంధ్రప్రదేశ్', 'ఆంధ్రప్రదేశ్', 'ఆంధ్రప్రదేశ్', 'తెలంగాణ', 'తెలంగాణ', 'తెలంగాణ', 'తెలంగాణ', 'ఆంధ్రప్రదేశ్', 'ఆంధ్రప్రదేశ్'],
    districts: ['కేంద్రం', 'కేంద్రం', 'కేంద్రం', 'కేంద్రం', 'కేంద్రం', 'కేంద్రం', 'కేంద్రం', 'కేంద్రం', 'కేంద్రం', 'కేంద్రం', 'కేంద్రం', 'కేంద్రం', 'కేంద్రం', 'కేంద్రం', 'కేంద్రం'],
    streets: ['ఎంజీ రోడ్', 'గవర్నర్ పేట', 'జగదాంబ సెంటర్', 'బ్రాడ్‌పేట', 'గ్రాండ్ ట్రంక్ రోడ్', 'కర్నూలు మెయిన్ రోడ్', 'కోటిపల్లి రోడ్', 'తిరుమల గిరి రోడ్', 'కడప మెయిన్ రోడ్', 'నిజామాబాద్ మెయిన్ రోడ్', 'ఖమ్మం మెయిన్ రోడ్', 'వరంగల్ మెయిన్ రోడ్', 'మహబూబ్‌నగర్ మెయిన్ రోడ్', 'అనంతపురం మెయిన్ రోడ్', 'చిత్తూరు మెయిన్ రోడ్'],
    postalCodes: ['500001', '520001', '530001', '522001', '524001', '518001', '533101', '517501', '516001', '503001', '507001', '506001', '509001', '515001', '517001'],
    format: 'standard'
  },

  // 印度卡纳达语 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'kn_IN': {
    cities: ['ಬೆಂಗಳೂರು', 'ಮೈಸೂರು', 'ಹುಬ್ಬಳ್ಳಿ', 'ಮಂಗಳೂರು', 'ಬೆಳಗಾವಿ', 'ದಾವಣಗೆರೆ', 'ಬಳ್ಳಾರಿ', 'ಬೀದರ್', 'ತುಮಕೂರು', 'ಶಿವಮೊಗ್ಗ', 'ರಾಯಚೂರು', 'ಕೊಪ್ಪಳ', 'ಗದಗ', 'ಬಾಗಲಕೋಟೆ', 'ಹಾಸನ'],
    states: ['ಕರ್ನಾಟಕ', 'ಕರ್ನಾಟಕ', 'ಕರ್ನಾಟಕ', 'ಕರ್ನಾಟಕ', 'ಕರ್ನಾಟಕ', 'ಕರ್ನಾಟಕ', 'ಕರ್ನಾಟಕ', 'ಕರ್ನಾಟಕ', 'ಕರ್ನಾಟಕ', 'ಕರ್ನಾಟಕ', 'ಕರ್ನಾಟಕ', 'ಕರ್ನಾಟಕ', 'ಕರ್ನಾಟಕ', 'ಕರ್ನಾಟಕ', 'ಕರ್ನಾಟಕ'],
    districts: ['ಕೇಂದ್ರ', 'ಕೇಂದ್ರ', 'ಕೇಂದ್ರ', 'ಕೇಂದ್ರ', 'ಕೇಂದ್ರ', 'ಕೇಂದ್ರ', 'ಕೇಂದ್ರ', 'ಕೇಂದ್ರ', 'ಕೇಂದ್ರ', 'ಕೇಂದ್ರ', 'ಕೇಂದ್ರ', 'ಕೇಂದ್ರ', 'ಕೇಂದ್ರ', 'ಕೇಂದ್ರ', 'ಕೇಂದ್ರ'],
    streets: ['ಎಂಜಿ ರೋಡ್', 'ಸಯ್ಯಾಜಿ ರಾವ್ ರೋಡ್', 'ಲಂಕೇಶ್ ರೋಡ್', 'ಫೋರ್ಸ್ ರೋಡ್', 'ಮಿಲಿಟರಿ ಮಾವಳ್', 'ಪಿಬಿ ರೋಡ್', 'ಕಂಪನಿ ಬಾಗ್', 'ಬೀದರ್ ರೋಡ್', 'ಸಿದ್ದಗಂಗ ರೋಡ್', 'ಕೆಂಪೇಗೌಡ ರೋಡ್', 'ಸ್ಟೇಷನ್ ರೋಡ್', 'ಗಂಗಾವತಿ ರೋಡ್', 'ಹುಬ್ಬಳ್ಳಿ ರೋಡ್', 'ನಾವಲಗುಂದ ರೋಡ್', 'ಬಿಎಂ ರೋಡ್'],
    postalCodes: ['560001', '570001', '580001', '575001', '590001', '577001', '583101', '585401', '572101', '577201', '584101', '583231', '582101', '587101', '573201'],
    format: 'standard'
  },

  // 印度马拉雅拉姆语 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'ml_IN': {
    cities: ['തിരുവനന്തപുരം', 'കൊച്ചി', 'കോഴിക്കോട്', 'തൃശ്ശൂർ', 'കൊല്ലം', 'ആലപ്പുഴ', 'പാലക്കാട്', 'കോട്ടയം', 'കണ്ണൂർ', 'മലപ്പുറം', 'കാസർകോട്', 'പത്തനംതിട്ട', 'ഇടുക്കി', 'വയനാട്', 'എറണാകുളം'],
    states: ['കേരളം', 'കേരളം', 'കേരളം', 'കേരളം', 'കേരളം', 'കേരളം', 'കേരളം', 'കേരളം', 'കേരളം', 'കേരളം', 'കേരളം', 'കേരളം', 'കേരളം', 'കേരളം', 'കേരളം'],
    districts: ['കേന്ദ്രം', 'കേന്ദ്രം', 'കേന്ദ്രം', 'കേന്ദ്രം', 'കേന്ദ്രം', 'കേന്ദ്രം', 'കേന്ദ്രം', 'കേന്ദ്രം', 'കേന്ദ്രം', 'കേന്ദ്രം', 'കേന്ദ്രം', 'കേന്ദ്രം', 'കേന്ദ്രം', 'കേന്ദ്രം', 'കേന്ദ്രം'],
    streets: ['എം.ജി റോഡ്', 'മറീൻ ഡ്രൈവ്', 'എസ്.എം സ്ട്രീറ്റ്', 'റൗണ്ട് ഈസ്റ്റ്', 'മെയിൻ റോഡ്', 'കുളക്കട റോഡ്', 'ജി.ബി റോഡ്', 'കെ.കെ റോഡ്', 'കാന്തീരവ നഗർ', 'മഞ്ചേരി റോഡ്', 'നാഷണൽ ഹൈവേ', 'എം.സി റോഡ്', 'ഹൈ റേഞ്ച് റോഡ്', 'കൽപ്പറ്റ റോഡ്', 'എം.ജി റോഡ്'],
    postalCodes: ['695001', '682001', '673001', '680001', '691001', '688001', '678001', '686001', '670001', '676101', '671121', '689645', '685501', '673121', '682001'],
    format: 'standard'
  },

  // 印度古吉拉特语 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'gu_IN': {
    cities: ['અમદાવાદ', 'સુરત', 'વડોદરા', 'રાજકોટ', 'ભાવનગર', 'જામનગર', 'જૂનાગઢ', 'ગાંધીનગર', 'નડિયાદ', 'આણંદ', 'મોરબી', 'સુરેન્દ્રનગર', 'ભરૂચ', 'વેરાવળ', 'ગોંડલ'],
    states: ['ગુજરાત', 'ગુજરાત', 'ગુજરાત', 'ગુજરાત', 'ગુજરાત', 'ગુજરાત', 'ગુજરાત', 'ગુજરાત', 'ગુજરાત', 'ગુજરાત', 'ગુજરાત', 'ગુજરાત', 'ગુજરાત', 'ગુજરાત', 'ગુજરાત'],
    districts: ['કેન્દ્ર', 'કેન્દ્ર', 'કેન્દ્ર', 'કેન્દ્ર', 'કેન્દ્ર', 'કેન્દ્ર', 'કેન્દ્ર', 'કેન્દ્ર', 'કેન્દ્ર', 'કેન્દ્ર', 'કેન્દ્ર', 'કેન્દ્ર', 'કેન્દ્ર', 'કેન્દ્ર', 'કેન્દ્ર'],
    streets: ['સી.જી. રોડ', 'રિંગ રોડ', 'સયાજીરાવ રોડ', 'જવાહર રોડ', 'ક્રાંતિ ચોક', 'બેડી બંદર રોડ', 'કલવા ચોક', 'સેક્ટર ૧', 'કોલેજ રોડ', 'વલ્લભ વિદ્યાનગર', 'મોરબી મેઈન રોડ', 'વાઘજીભાઈ રોડ', 'નર્મદા રોડ', 'સોમનાથ રોડ', 'રાજકોટ રોડ'],
    postalCodes: ['380001', '395001', '390001', '360001', '364001', '361001', '362001', '382010', '387001', '388001', '363641', '363001', '392001', '362266', '360311'],
    format: 'standard'
  },

  // 印度旁遮普语 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'pa_IN': {
    cities: ['ਲੁਧਿਆਣਾ', 'ਅੰਮ੍ਰਿਤਸਰ', 'ਜਲੰਧਰ', 'ਪਟਿਆਲਾ', 'ਬਠਿੰਡਾ', 'ਮੋਹਾਲੀ', 'ਪਠਾਨਕੋਟ', 'ਹੁਸ਼ਿਆਰਪੁਰ', 'ਬਟਾਲਾ', 'ਮਾਲੇਰਕੋਟਲਾ', 'ਖੰਨਾ', 'ਫਿਰੋਜ਼ਪੁਰ', 'ਮੋਗਾ', 'ਅਬੋਹਰ', 'ਫਗਵਾੜਾ'],
    states: ['ਪੰਜਾਬ', 'ਪੰਜਾਬ', 'ਪੰਜਾਬ', 'ਪੰਜਾਬ', 'ਪੰਜਾਬ', 'ਪੰਜਾਬ', 'ਪੰਜਾਬ', 'ਪੰਜਾਬ', 'ਪੰਜਾਬ', 'ਪੰਜਾਬ', 'ਪੰਜਾਬ', 'ਪੰਜਾਬ', 'ਪੰਜਾਬ', 'ਪੰਜਾਬ', 'ਪੰਜਾਬ'],
    districts: ['ਕੇਂਦਰ', 'ਕੇਂਦਰ', 'ਕੇਂਦਰ', 'ਕੇਂਦਰ', 'ਕੇਂਦਰ', 'ਕੇਂਦਰ', 'ਕੇਂਦਰ', 'ਕੇਂਦਰ', 'ਕੇਂਦਰ', 'ਕੇਂਦਰ', 'ਕੇਂਦਰ', 'ਕੇਂਦਰ', 'ਕੇਂਦਰ', 'ਕੇਂਦਰ', 'ਕੇਂਦਰ'],
    streets: ['ਮਾਲ ਰੋਡ', 'ਲਾਰੈਂਸ ਰੋਡ', 'ਮਾਡਲ ਟਾਊਨ', 'ਸ਼ੇਰਾਨਵਾਲਾ ਗੇਟ', 'ਮਾਲ ਰੋਡ', 'ਸੈਕਟਰ ੭੦', 'ਡਲਹੌਜ਼ੀ ਰੋਡ', 'ਜੀ.ਟੀ. ਰੋਡ', 'ਕਸੂਰ ਰੋਡ', 'ਸੰਗਰੂਰ ਰੋਡ', 'ਜੀ.ਟੀ. ਰੋਡ', 'ਮਾਲ ਰੋਡ', 'ਮਾਲ ਰੋਡ', 'ਮਾਲ ਰੋਡ', 'ਜੀ.ਟੀ. ਰੋਡ'],
    postalCodes: ['141001', '143001', '144001', '147001', '151001', '160055', '145001', '146001', '143505', '148023', '141401', '152002', '142001', '152116', '144401'],
    format: 'standard'
  },

  // 印度马拉地语 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'mr_IN': {
    cities: ['मुंबई', 'पुणे', 'नागपूर', 'ठाणे', 'नाशिक', 'औरंगाबाद', 'सोलापूर', 'अमरावती', 'कोल्हापूर', 'अकोला', 'लातूर', 'धुळे', 'सांगली', 'जळगाव', 'नांदेड'],
    states: ['महाराष्ट्र', 'महाराष्ट्र', 'महाराष्ट्र', 'महाराष्ट्र', 'महाराष्ट्र', 'महाराष्ट्र', 'महाराष्ट्र', 'महाराष्ट्र', 'महाराष्ट्र', 'महाराष्ट्र', 'महाराष्ट्र', 'महाराष्ट्र', 'महाराष्ट्र', 'महाराष्ट्र', 'महाराष्ट्र'],
    districts: ['केंद्र', 'केंद्र', 'केंद्र', 'केंद्र', 'केंद्र', 'केंद्र', 'केंद्र', 'केंद्र', 'केंद्र', 'केंद्र', 'केंद्र', 'केंद्र', 'केंद्र', 'केंद्र', 'केंद्र'],
    streets: ['मरीन ड्राइव्ह', 'एफसी रोड', 'सिव्हिल लाईन्स', 'गोखले रोड', 'मुंबई नाका', 'स्टेशन रोड', 'सिद्धेश्वर रोड', 'बडनेरा रोड', 'रंकाळा', 'राजापेठ', 'बारशी रोड', 'अगरा रोड', 'मिरज रोड', 'नवी पेठ', 'स्टेशन रोड'],
    postalCodes: ['400001', '411001', '440001', '400601', '422001', '431001', '413001', '444601', '416001', '444001', '413512', '424001', '416416', '425001', '431601'],
    format: 'standard'
  },

  // 埃及阿拉伯语 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'ar_EG': {
    cities: ['القاهرة', 'الإسكندرية', 'الجيزة', 'شبرا الخيمة', 'بورسعيد', 'السويس', 'الأقصر', 'المنصورة', 'طنطا', 'أسيوط', 'الإسماعيلية', 'الفيوم', 'الزقازيق', 'دمياط', 'أسوان'],
    states: ['القاهرة', 'الإسكندرية', 'الجيزة', 'القليوبية', 'بورسعيد', 'السويس', 'الأقصر', 'الدقهلية', 'الغربية', 'أسيوط', 'الإسماعيلية', 'الفيوم', 'الشرقية', 'دمياط', 'أسوان'],
    districts: ['وسط البلد', 'وسط البلد', 'وسط البلد', 'وسط البلد', 'وسط البلد', 'وسط البلد', 'وسط البلد', 'وسط البلد', 'وسط البلد', 'وسط البلد', 'وسط البلد', 'وسط البلد', 'وسط البلد', 'وسط البلد', 'وسط البلد'],
    streets: ['شارع التحرير', 'كورنيش الإسكندرية', 'شارع الهرم', 'شارع شبرا', 'شارع الجمهورية', 'شارع الأربعين', 'شارع الكرنك', 'شارع الجمهورية', 'شارع الجلاء', 'شارع الثورة', 'شارع الجيش', 'شارع الجمهورية', 'شارع الجلاء', 'شارع البحر', 'كورنيش النيل'],
    postalCodes: ['11511', '21500', '12411', '13511', '42511', '43511', '85511', '35511', '31511', '71511', '41511', '63511', '44511', '34511', '81511'],
    format: 'standard'
  },

  // 摩洛哥法语 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'fr_MA': {
    cities: ['Casablanca', 'Rabat', 'Fès', 'Marrakech', 'Agadir', 'Tanger', 'Meknès', 'Oujda', 'Kenitra', 'Tétouan', 'Safi', 'Mohammedia', 'Khouribga', 'El Jadida', 'Béni Mellal'],
    states: ['Casablanca-Settat', 'Rabat-Salé-Kénitra', 'Fès-Meknès', 'Marrakech-Safi', 'Souss-Massa', 'Tanger-Tétouan-Al Hoceïma', 'Fès-Meknès', 'Oriental', 'Rabat-Salé-Kénitra', 'Tanger-Tétouan-Al Hoceïma', 'Marrakech-Safi', 'Casablanca-Settat', 'Béni Mellal-Khénifra', 'Casablanca-Settat', 'Béni Mellal-Khénifra'],
    districts: ['Centre-ville', 'Centre-ville', 'Médina', 'Médina', 'Centre-ville', 'Centre-ville', 'Médina', 'Centre-ville', 'Centre-ville', 'Centre-ville', 'Centre-ville', 'Centre-ville', 'Centre-ville', 'Centre-ville', 'Centre-ville'],
    streets: ['Boulevard Mohammed V', 'Avenue Mohammed V', 'Avenue Hassan II', 'Avenue Mohammed V', 'Boulevard du 20 Août', 'Boulevard Pasteur', 'Avenue des FAR', 'Boulevard Derfoufi', 'Avenue Mohammed V', 'Avenue Mohammed V', 'Avenue Zerktouni', 'Boulevard Hassan II', 'Avenue Hassan II', 'Boulevard Ziraoui', 'Avenue Hassan II'],
    postalCodes: ['20000', '10000', '30000', '40000', '80000', '90000', '50000', '60000', '14000', '93000', '46000', '28000', '25000', '24000', '23000'],
    format: 'standard'
  },

  // 安哥拉葡萄牙语 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'pt_AO': {
    cities: ['Luanda', 'Huambo', 'Lobito', 'Benguela', 'Kuito', 'Lubango', 'Malanje', 'Namibe', 'Soyo', 'Cabinda', 'Uíge', 'Saurimo', 'Menongue', 'Mbanza-Kongo', 'Ondjiva'],
    states: ['Luanda', 'Huambo', 'Benguela', 'Benguela', 'Bié', 'Huíla', 'Malanje', 'Namibe', 'Zaire', 'Cabinda', 'Uíge', 'Lunda Sul', 'Cuando Cubango', 'Zaire', 'Cunene'],
    districts: ['Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro'],
    streets: ['Avenida 4 de Fevereiro', 'Rua José Martí', 'Avenida da Independência', 'Rua Direita', 'Avenida da Liberdade', 'Avenida 4 de Abril', 'Rua Deolinda Rodrigues', 'Avenida Marginal', 'Rua Principal', 'Avenida Marien Ngouabi', 'Rua Comandante Dangereux', 'Avenida da Revolução', 'Rua da Missão', 'Rua do Comércio', 'Avenida de Namibe'],
    postalCodes: ['1000', '3000', '2000', '2100', '3100', '4000', '3200', '2300', '1100', '1200', '3300', '1300', '4100', '1400', '4200'],
    format: 'standard'
  },

  // 肯尼亚斯瓦希里语 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'sw_KE': {
    cities: ['Nairobi', 'Mombasa', 'Kisumu', 'Nakuru', 'Eldoret', 'Thika', 'Malindi', 'Kitale', 'Garissa', 'Kakamega', 'Machakos', 'Meru', 'Nyeri', 'Kericho', 'Embu'],
    states: ['Nairobi', 'Mombasa', 'Kisumu', 'Nakuru', 'Uasin Gishu', 'Kiambu', 'Kilifi', 'Trans-Nzoia', 'Garissa', 'Kakamega', 'Machakos', 'Meru', 'Nyeri', 'Kericho', 'Embu'],
    districts: ['CBD', 'CBD', 'CBD', 'CBD', 'CBD', 'CBD', 'CBD', 'CBD', 'CBD', 'CBD', 'CBD', 'CBD', 'CBD', 'CBD', 'CBD'],
    streets: ['Kenyatta Avenue', 'Moi Avenue', 'Oginga Odinga Street', 'Kenyatta Avenue', 'Uganda Road', 'Commercial Street', 'Lamu Road', 'Kenyatta Street', 'Garissa Road', 'Mumias Road', 'Machakos Road', 'Kenyatta Road', 'Kimathi Street', 'Moi Highway', 'Embu-Meru Road'],
    postalCodes: ['00100', '80100', '40100', '20100', '30100', '01000', '80200', '30200', '70100', '50100', '90100', '60200', '10100', '20200', '60100'],
    format: 'standard'
  },

  // 埃塞俄比亚阿姆哈拉语 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'am_ET': {
    cities: ['አዲስ አበባ', 'ድሬ ዳዋ', 'መቀሌ', 'ጎንደር', 'አዋሳ', 'ባህር ዳር', 'ደሴ', 'ጅማ', 'ሻሸመኔ', 'ሶዶ', 'አርባ ምንጭ', 'ሐረር', 'ደብረ ማርቆስ', 'ደብረ ብርሃን', 'አሶሳ'],
    states: ['አዲስ አበባ', 'ድሬ ዳዋ', 'ትግራይ', 'አማራ', 'ደቡብ', 'አማራ', 'አማራ', 'ኦሮሚያ', 'ኦሮሚያ', 'ደቡብ', 'ደቡብ', 'ሐረሪ', 'አማራ', 'አማራ', 'ቤንሻንጉል ጉሙዝ'],
    districts: ['ከተማ ማእከል', 'ከተማ ማእከል', 'ከተማ ማእከል', 'ከተማ ማእከል', 'ከተማ ማእከል', 'ከተማ ማእከል', 'ከተማ ማእከል', 'ከተማ ማእከል', 'ከተማ ማእከል', 'ከተማ ማእከል', 'ከተማ ማእከል', 'ከተማ ማእከል', 'ከተማ ማእከል', 'ከተማ ማእከል', 'ከተማ ማእከል'],
    streets: ['ቦሌ መንገድ', 'ሐረር መንገድ', 'አዲስ አለም', 'ፋሲል መንገድ', 'ሐዋሳ መንገድ', 'ባህር ዳር መንገድ', 'ደሴ መንገድ', 'ጅማ መንገድ', 'ሻሸመኔ መንገድ', 'ሶዶ መንገድ', 'አርባ ምንጭ መንገድ', 'ሐረር መንገድ', 'ደብረ ማርቆስ መንገድ', 'ደብረ ብርሃን መንገድ', 'አሶሳ መንገድ'],
    postalCodes: ['1000', '1001', '1002', '1003', '1004', '1005', '1006', '1007', '1008', '1009', '1010', '1011', '1012', '1013', '1014'],
    format: 'standard'
  },

  // 墨西哥西班牙语 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'es_MX': {
    cities: ['Ciudad de México', 'Guadalajara', 'Monterrey', 'Puebla', 'Tijuana', 'León', 'Juárez', 'Torreón', 'Querétaro', 'San Luis Potosí', 'Mérida', 'Mexicali', 'Aguascalientes', 'Cuernavaca', 'Saltillo'],
    states: ['Ciudad de México', 'Jalisco', 'Nuevo León', 'Puebla', 'Baja California', 'Guanajuato', 'Chihuahua', 'Coahuila', 'Querétaro', 'San Luis Potosí', 'Yucatán', 'Baja California', 'Aguascalientes', 'Morelos', 'Coahuila'],
    districts: ['Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro'],
    streets: ['Avenida Reforma', 'Avenida Vallarta', 'Avenida Constitución', 'Avenida Juárez', 'Avenida Revolución', 'Boulevard López Mateos', 'Avenida Juárez', 'Boulevard Independencia', 'Avenida Zaragoza', 'Avenida Carranza', 'Paseo de Montejo', 'Avenida Reforma', 'Avenida Madero', 'Avenida Morelos', 'Boulevard Venustiano Carranza'],
    postalCodes: ['06000', '44100', '64000', '72000', '22000', '37000', '32000', '27000', '76000', '78000', '97000', '21000', '20000', '62000', '25000'],
    format: 'standard'
  },

  // 阿根廷西班牙语 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'es_AR': {
    cities: ['Buenos Aires', 'Córdoba', 'Rosario', 'Mendoza', 'Tucumán', 'La Plata', 'Mar del Plata', 'Salta', 'Santa Fe', 'San Juan', 'Resistencia', 'Santiago del Estero', 'Corrientes', 'Posadas', 'Bahía Blanca'],
    states: ['Buenos Aires', 'Córdoba', 'Santa Fe', 'Mendoza', 'Tucumán', 'Buenos Aires', 'Buenos Aires', 'Salta', 'Santa Fe', 'San Juan', 'Chaco', 'Santiago del Estero', 'Corrientes', 'Misiones', 'Buenos Aires'],
    districts: ['Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro'],
    streets: ['Avenida Corrientes', 'Avenida Colón', 'Avenida Pellegrini', 'Avenida San Martín', 'Avenida Mate de Luna', 'Avenida 7', 'Avenida Luro', 'Avenida Belgrano', 'Avenida Freyre', 'Avenida Libertador', 'Avenida Alberdi', 'Avenida Belgrano', 'Avenida Costanera', 'Avenida Mitre', 'Avenida Alem'],
    postalCodes: ['C1000', 'X5000', 'S2000', 'M5500', 'T4000', 'B1900', 'B7600', 'A4400', 'S3000', 'J5400', 'H3500', 'G4200', 'W3400', 'N3300', 'B8000'],
    format: 'standard'
  },

  // 哥伦比亚西班牙语 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'es_CO': {
    cities: ['Bogotá', 'Medellín', 'Cali', 'Barranquilla', 'Cartagena', 'Cúcuta', 'Bucaramanga', 'Pereira', 'Santa Marta', 'Ibagué', 'Pasto', 'Manizales', 'Neiva', 'Villavicencio', 'Armenia'],
    states: ['Bogotá D.C.', 'Antioquia', 'Valle del Cauca', 'Atlántico', 'Bolívar', 'Norte de Santander', 'Santander', 'Risaralda', 'Magdalena', 'Tolima', 'Nariño', 'Caldas', 'Huila', 'Meta', 'Quindío'],
    districts: ['Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro'],
    streets: ['Carrera 7', 'Carrera 70', 'Avenida Sexta', 'Carrera 44', 'Avenida Blas de Lezo', 'Avenida Libertadores', 'Carrera 27', 'Avenida Circunvalar', 'Carrera 1', 'Carrera 3', 'Carrera 25', 'Carrera 23', 'Carrera 5', 'Carrera 40', 'Carrera 14'],
    postalCodes: ['110111', '050001', '760001', '080001', '130001', '540001', '680001', '660001', '470001', '730001', '520001', '170001', '410001', '500001', '630001'],
    format: 'standard'
  },

  // 秘鲁西班牙语 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'es_PE': {
    cities: ['Lima', 'Arequipa', 'Trujillo', 'Chiclayo', 'Piura', 'Iquitos', 'Cusco', 'Chimbote', 'Huancayo', 'Tacna', 'Ica', 'Juliaca', 'Pucallpa', 'Cajamarca', 'Sullana'],
    states: ['Lima', 'Arequipa', 'La Libertad', 'Lambayeque', 'Piura', 'Loreto', 'Cusco', 'Áncash', 'Junín', 'Tacna', 'Ica', 'Puno', 'Ucayali', 'Cajamarca', 'Piura'],
    districts: ['Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro'],
    streets: ['Jirón de la Unión', 'Calle Mercaderes', 'Jirón Pizarro', 'Avenida Balta', 'Avenida Grau', 'Jirón Próspero', 'Avenida El Sol', 'Avenida Pardo', 'Calle Real', 'Avenida Bolognesi', 'Boulevard El Ejército', 'Jirón 2 de Mayo', 'Jirón Ucayali', 'Jirón Dos de Mayo', 'Calle San Martín'],
    postalCodes: ['15001', '04001', '13001', '14001', '20001', '16001', '08001', '02801', '12001', '23001', '11001', '21101', '25001', '06001', '20103'],
    format: 'standard'
  },

  // 智利西班牙语 - 格式：姓名/街道/邮编/城市/国家/邮箱
  'es_CL': {
    cities: ['Santiago', 'Valparaíso', 'Concepción', 'La Serena', 'Antofagasta', 'Temuco', 'Rancagua', 'Talca', 'Arica', 'Chillán', 'Iquique', 'Los Ángeles', 'Puerto Montt', 'Calama', 'Copiapó'],
    states: ['Región Metropolitana', 'Valparaíso', 'Biobío', 'Coquimbo', 'Antofagasta', 'Araucanía', 'O\'Higgins', 'Maule', 'Arica y Parinacota', 'Ñuble', 'Tarapacá', 'Biobío', 'Los Lagos', 'Antofagasta', 'Atacama'],
    districts: ['Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro', 'Centro'],
    streets: ['Avenida Libertador Bernardo O\'Higgins', 'Avenida Pedro Montt', 'Avenida O\'Higgins', 'Avenida Francisco de Aguirre', 'Avenida Angamos', 'Avenida Alemania', 'Avenida Libertador Bernardo O\'Higgins', 'Avenida San Miguel', 'Avenida Santa María', 'Avenida Libertad', 'Avenida Arturo Prat', 'Avenida Colón', 'Avenida Diego Portales', 'Avenida Granaderos', 'Avenida Copayapu'],
    postalCodes: ['8320000', '2340000', '4030000', '1700000', '1240000', '4780000', '2820000', '3460000', '1000000', '3780000', '1100000', '4440000', '5480000', '1390000', '1530000'],
    format: 'standard'
  }
};// 随机选择函数
function getRandomItem<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

// 生成随机数字
function getRandomNumber(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 生成随机邮箱
function generateEmail(firstName: string, lastName: string): string {
  const domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'qq.com', '163.com', 'sina.com'];
  const cleanFirst = firstName.toLowerCase().replace(/[^a-z0-9]/g, '');
  const cleanLast = lastName.toLowerCase().replace(/[^a-z0-9]/g, '');
  const domain = getRandomItem(domains);
  
  const patterns = [
    `${cleanFirst}.${cleanLast}@${domain}`,
    `${cleanFirst}${cleanLast}@${domain}`,
    `${cleanFirst}${getRandomNumber(1, 999)}@${domain}`,
    `${cleanLast}${cleanFirst}@${domain}`
  ];
  
  return getRandomItem(patterns);
}

// 生成随机电话号码
function generatePhoneNumber(locale: string): string {
  const phoneFormats: Record<string, string[]> = {
    'zh_CN': ['+86 138-0013-8000', '+86 139-0013-9000', '+86 150-0015-0000'],
    'zh_TW': ['+886 912-345-678', '+886 987-654-321', '+886 955-123-456'],
    'ja_JP': ['+81 90-1234-5678', '+81 80-9876-5432', '+81 70-1111-2222'],
    'ko_KR': ['+82 10-1234-5678', '+82 11-9876-5432', '+82 16-1111-2222'],
    'en_US': ['+****************', '+****************', '+****************'],
    'en_GB': ['+44 20 7946 0958', '+44 ************', '+44 ************'],
    'de_DE': ['+49 30 12345678', '+49 40 87654321', '+49 89 11112222'],
    'fr_FR': ['+33 1 42 86 83 26', '+33 4 91 15 36 47', '+33 5 56 00 00 00']
  };
  
  const formats = phoneFormats[locale] || phoneFormats['en_US'];
  return getRandomItem(formats);
}

// 生成完整地址 - 按照7行标准格式
function generateAddress(locale: string): {
  address: string, // 完整的7行格式地址
  city: string,
  state: string,
  postalCode: string,
  country: string,
  street: string, // 单独的街道信息
  district: string // 单独的区县信息
} {
  const localeInfo = getLocaleInfo(locale);
  const addressData = ADDRESS_DATABASE[locale];

  if (localeInfo && !addressData) {
    // 使用新配置生成地址
    const street = `${getRandomNumber(1, 9999)} Main Street`;
    const city = getRandomItem(localeInfo.states);
    const state = getRandomItem(localeInfo.states);
    const district = 'District';
    const postalCode = getRandomNumber(10000, 99999).toString();

    const address = `${street}\n${city}\n${state} ${district}`;

    return {
      address,
      city,
      state,
      postalCode,
      country: localeInfo.country,
      street,
      district
    };
  }

  if (!addressData) {
    // 默认使用美国地址格式
    const usData = ADDRESS_DATABASE['en_US'];
    const street = `${getRandomNumber(1, 9999)} ${getRandomItem(usData.streets)}`;
    const city = getRandomItem(usData.cities);
    const state = getRandomItem(usData.states!);
    const district = getRandomItem(usData.districts);
    const postalCode = getRandomItem(usData.postalCodes);

    // 美国标准格式：街道/城市/州区合并
    const address = `${street}\n${city}\n${state} ${district}`;

    return {
      address,
      city,
      state,
      postalCode,
      country: 'United States',
      street,
      district
    };
  }

  // 对于日本地址，不使用数字前缀
  const street = locale === 'ja_JP'
    ? getRandomItem(addressData.streets)
    : `${getRandomNumber(1, 999)} ${getRandomItem(addressData.streets)}`;
  const city = getRandomItem(addressData.cities);
  const postalCode = getRandomItem(addressData.postalCodes);
  const state = addressData.states ? getRandomItem(addressData.states) : city;
  const district = getRandomItem(addressData.districts);

  // 根据locale确定国家名称 - 统一使用英文名称
  const countryNames: Record<string, string> = {
    'zh_CN': 'China', 'zh_TW': 'Taiwan', 'ja_JP': 'Japan', 'ko_KR': 'South Korea',
    'en_US': 'United States', 'en_GB': 'United Kingdom', 'en_CA': 'Canada', 'en_IN': 'India',
    'th_TH': 'Thailand', 'vi_VN': 'Vietnam', 'ms_MY': 'Malaysia', 'id_ID': 'Indonesia',
    'en_HK': 'Hong Kong', 'en_SG': 'Singapore', 'en_AU': 'Australia', 'en_PH': 'Philippines',
    'bn_BD': 'Bangladesh', 'pt_BR': 'Brazil',
    'de_DE': 'Germany', 'de_AT': 'Austria', 'de_CH': 'Switzerland',
    'fr_FR': 'France', 'fr_CA': 'Canada', 'fr_CH': 'Switzerland', 'fr_BE': 'Belgium',
    'it_IT': 'Italy', 'it_CH': 'Switzerland', 'es_ES': 'Spain',
    'nl_NL': 'Netherlands', 'nl_BE': 'Belgium', 'pt_PT': 'Portugal',
    'ru_RU': 'Russia', 'sv_SE': 'Sweden', 'nb_NO': 'Norway', 'da_DK': 'Denmark',
    'fi_FI': 'Finland', 'is_IS': 'Iceland', 'pl_PL': 'Poland', 'en_NZ': 'New Zealand',
    'ne_NP': 'Nepal', 'fa_IR': 'Iran', 'he_IL': 'Israel', 'tr_TR': 'Turkey',
    'at_AT': 'Austria', 'el_GR': 'Greece', 'el_CY': 'Cyprus',
    // 新增的东欧巴尔干地区
    'cs_CZ': 'Czech Republic', 'sk_SK': 'Slovakia', 'hu_HU': 'Hungary', 'ro_RO': 'Romania',
    'bg_BG': 'Bulgaria', 'hr_HR': 'Croatia', 'sr_RS': 'Serbia', 'sl_SI': 'Slovenia',
    'mk_MK': 'North Macedonia', 'sq_AL': 'Albania',
    // 新增的亚洲地区
    'ar_SA': 'Saudi Arabia', 'ur_PK': 'Pakistan', 'hi_IN': 'India', 'ta_IN': 'India',
    'te_IN': 'India', 'kn_IN': 'India', 'ml_IN': 'India', 'gu_IN': 'India',
    'pa_IN': 'India', 'mr_IN': 'India',
    // 新增的非洲地区
    'ar_EG': 'Egypt', 'fr_MA': 'Morocco', 'pt_AO': 'Angola', 'sw_KE': 'Kenya', 'am_ET': 'Ethiopia',
    // 新增的美洲地区
    'es_MX': 'Mexico', 'es_AR': 'Argentina', 'es_CO': 'Colombia', 'es_PE': 'Peru', 'es_CL': 'Chile'
  };

  const country = countryNames[locale] || 'Unknown';

  // 根据地址格式生成严格的7行地址
  let address: string;
  if (addressData.format === 'reverse') {
    // 亚洲格式：大到小
    if (locale === 'zh_TW') {
      // 台湾格式：县市 + 街道 + 区 (city已经包含完整县市名称)
      address = `${city}\n${street}\n${district}`;
    } else if (locale === 'ja_JP') {
      // 日本格式：都道府县 + 市区町村 + 地名 + 丁目 + 番地-号
      const banchi = getRandomNumber(1, 20);
      const go = getRandomNumber(1, 15);
      const japaneseAddress = `${district}${street}${banchi}番${go}号`;
      address = `${state}${city}\n${japaneseAddress}`;
    } else if (locale === 'ko_KR') {
      // 韩国格式：道/特别市/广域市 + 街道 + 区 (state已经包含完整的道/市名称)
      address = `${state}\n${street}\n${district}`;
    } else {
      // 其他亚洲格式：省市合并 + 街道 + 区
      address = `${state}${city}\n${street}\n${district}`;
    }
  } else {
    // 西方格式：小到大 (街道 + 城市 + 州区合并)
    address = `${street}\n${city}\n${state} ${district}`;
  }

  return {
    address,
    city,
    state,
    postalCode,
    country,
    street, // 单独的街道信息
    district // 单独的区县信息
  };
}

// 主要的本地生成函数
export function generateLocalBillToInfo(locale: string): BillToInfo {
  const localeInfo = getLocaleInfo(locale);
  const nameData = NAME_DATABASE[locale];

  if (localeInfo && !nameData) {
    // 使用新配置生成姓名
    const firstName = getRandomItem(localeInfo.firstNames);
    const lastName = getRandomItem(localeInfo.lastNames);
    const address = generateAddress(locale);

    return {
      name: `${firstName} ${lastName}`,
      company: `${lastName} Corp`,
      email: generateEmail(firstName, lastName),
      phone: generatePhoneNumber(locale),
      address: address.address,
      city: address.city,
      state: address.state,
      postalCode: address.postalCode,
      country: address.country,
      street: address.street,
      district: address.district
    };
  }

  if (!nameData) {
    // 如果没有找到对应的locale，使用英文作为默认
    const defaultData = NAME_DATABASE['en_US'];
    const firstName = getRandomItem(defaultData.firstNames);
    const lastName = getRandomItem(defaultData.lastNames);
    const address = generateAddress('en_US');

    return {
      name: `${firstName} ${lastName}`,
      company: `${lastName} Corp`,
      email: generateEmail(firstName, lastName),
      phone: generatePhoneNumber('en_US'),
      address: address.address, // 使用完整的7行地址格式
      city: address.city,
      state: address.state,
      postalCode: address.postalCode,
      country: address.country,
      street: address.street,
      district: address.district
    };
  }
  
  const firstName = getRandomItem(nameData.firstNames);
  const lastName = getRandomItem(nameData.lastNames);
  const address = generateAddress(locale);
  
  return {
    name: `${firstName} ${lastName}`,
    company: `${lastName} Company`,
    email: generateEmail(firstName, lastName),
    phone: generatePhoneNumber(locale),
    address: address.address, // 使用完整的7行地址格式
    city: address.city,
    state: address.state,
    postalCode: address.postalCode,
    country: address.country,
    street: address.street,
    district: address.district
  };
}

// 检查是否支持某个locale
export function isLocaleSupported(locale: string): boolean {
  return locale in NAME_DATABASE;
}

// 获取所有支持的locale列表
export function getSupportedLocales(): string[] {
  return Object.keys(NAME_DATABASE);
}

// 测试函数 - 生成示例地址用于验证格式
export function testAddressGeneration() {
  console.log('=== 地址格式测试 ===');

  // 测试中国地址格式
  const cnAddress = generateAddress('zh_CN');
  console.log('中国地址格式:');
  console.log(`邮编: ${cnAddress.postalCode}`);
  console.log(`地址: ${cnAddress.address}`);
  console.log(`国家: ${cnAddress.country}`);
  console.log('---');

  // 测试美国地址格式
  const usAddress = generateAddress('en_US');
  console.log('美国地址格式:');
  console.log(`邮编: ${usAddress.postalCode}`);
  console.log(`地址: ${usAddress.address}`);
  console.log(`国家: ${usAddress.country}`);
  console.log('---');

  // 测试日本地址格式
  const jpAddress = generateAddress('ja_JP');
  console.log('日本地址格式:');
  console.log(`邮编: ${jpAddress.postalCode}`);
  console.log(`地址: ${jpAddress.address}`);
  console.log(`国家: ${jpAddress.country}`);
}