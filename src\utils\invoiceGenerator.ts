import { InvoiceData, BillToInfo, InvoiceType, CompanyInfo } from '@/types/invoice';
import { generateLocalBillToInfo } from './fakerAlternative';
import { getLocaleInfo } from './localeConfig';

// 随机生成Invoice号码 (格式: 8位十六进制数字-000+1位随机数字)
function generateInvoiceNumber(): string {
  const hexChars = '0123456789ABCDEF';
  let hexResult = '';
  for (let i = 0; i < 8; i++) {
    hexResult += hexChars.charAt(Math.floor(Math.random() * hexChars.length));
  }
  const lastDigit = Math.floor(Math.random() * 10);
  return `${hexResult}-000${lastDigit}`;
}

// 随机生成收据号码 (格式: 4位数字-4位数字)
function generateReceiptNumber(): string {
  const part1 = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  const part2 = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `${part1}-${part2}`;
}

// 随机生成支付方式
function generatePaymentMethod(): string {
  const cardTypes = ['Visa', 'MasterCard', 'American Express', 'Discover'];
  const randomCardType = cardTypes[Math.floor(Math.random() * cardTypes.length)];
  const lastFourDigits = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `${randomCardType} - ${lastFourDigits}`;
}

// 随机生成日期 (2025年4月17日至2025年6月16日)
function generateRandomDate(): string {
  const startDate = new Date('2025-04-17');
  const endDate = new Date('2025-06-16');
  const timeDiff = endDate.getTime() - startDate.getTime();
  const randomTime = Math.random() * timeDiff;
  const randomDate = new Date(startDate.getTime() + randomTime);
  
  return randomDate.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

// 根据 locale 获取国家和州/省信息
function getLocationInfoByLocale(locale: string) {
  const localeInfo = getLocaleInfo(locale);

  if (localeInfo) {
    return {
      country: localeInfo.country,
      states: localeInfo.states
    };
  }

  // 如果没有找到配置，返回默认值
  return {
    country: 'United States',
    states: ['California', 'New York', 'Texas', 'Florida', 'Illinois']
  };

}



// 纯API调用生成收票人信息（不回退到本地生成器）
async function generateBillToInfoFromAPI(email: string, locale: string = 'en_US'): Promise<BillToInfo> {
  const startTime = Date.now();
  console.log(`🌐 尝试调用 Faker API (${locale})...`);

  try {
    // 使用 AbortController 实现超时
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000); // 3秒超时

    const response = await fetch(`https://fakerapi.it/api/v2/persons?_quantity=1&_locale=${locale}`, {
      signal: controller.signal,
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'WIPDF-Invoice-Generator'
      }
    });

    clearTimeout(timeoutId);
    const data = await response.json();
    const apiTime = Date.now() - startTime;
    console.log(`✅ Faker API 响应成功 (${apiTime}ms)`);

    if (data.status === 'OK' && data.data && data.data.length > 0) {
      const person = data.data[0];
      const locationInfo = getLocationInfoByLocale(locale);

      // 随机选择一个州/省
      const randomState = locationInfo.states[Math.floor(Math.random() * locationInfo.states.length)];

      // 根据不同国家格式化地址
      if (locale === 'zh_CN') {
        // 中文地址格式
        return {
          name: `${person.firstname}${person.lastname}`,
          company: `${person.lastname}公司`,
          email: email, // 使用用户输入的邮箱
          phone: `+86 138-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
          address: person.address.street,
          city: person.address.city,
          state: randomState,
          postalCode: person.address.zipcode,
          country: locationInfo.country
        };
      } else if (locale === 'ja_JP') {
        // 日文地址格式
        return {
          name: `${person.firstname} ${person.lastname}`,
          company: `${person.lastname}株式会社`,
          email: email, // 使用用户输入的邮箱
          phone: `+81 90-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
          address: person.address.street,
          city: person.address.city,
          state: randomState,
          postalCode: person.address.zipcode,
          country: locationInfo.country
        };
      } else {
        // 西方国家地址格式
        return {
          name: `${person.firstname} ${person.lastname}`.toUpperCase(),
          company: `${person.lastname} Corp`,
          email: email, // 使用用户输入的邮箱
          phone: `******-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
          address: person.address.street,
          city: person.address.city,
          state: randomState,
          postalCode: person.address.zipcode,
          country: locationInfo.country
        };
      }
    }

    // 如果API返回的数据格式不正确，抛出错误
    throw new Error('API返回的数据格式不正确');
  } catch (error) {
    const apiTime = Date.now() - startTime;
    console.warn(`❌ Faker API 失败 (${apiTime}ms):`, error instanceof Error ? error.message : error);
    throw error; // 重新抛出错误，不进行回退
  }
}



// 生成日期范围 (基于支付日期)
function generateDateRange(datePaid: string): string {
  const paidDate = new Date(datePaid);
  const startDate = new Date(paidDate);
  const endDate = new Date(paidDate);
  endDate.setMonth(endDate.getMonth() + 1);
  
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };
  
  return `${formatDate(startDate)} - ${formatDate(endDate)}, ${paidDate.getFullYear()}`;
}

// 生成公司信息
function generateCompanyInfo(type: InvoiceType): CompanyInfo {
  if (type === InvoiceType.WINDSURF || type === InvoiceType.WINDSURF_15) {
    return {
      name: 'Windsurf',
      address: '900 Villa Street',
      city: 'Mountain View',
      state: 'California',
      postalCode: '94041',
      country: 'United States',
      email: '<EMAIL>',
      taxInfo: 'EU OSS VAT EU372077851'
    };
  } else {
    return {
      name: 'Cursor',
      address: '801 West End Avenue',
      city: 'New York',
      state: 'New York',
      postalCode: '10025',
      country: 'United States',
      phone: '******-425-9504',
      email: '<EMAIL>',
      taxInfo: 'Anysphere, Inc.\nUS EIN 87-4436547'
    };
  }
}

// 根据发票类型生成产品信息
function generateProductInfo(type: InvoiceType): { amount: string; description: string } {
  if (type === InvoiceType.WINDSURF) {
    return {
      amount: '$6.90',
      description: 'Windsurf Pro'
    };
  } else if (type === InvoiceType.WINDSURF_15) {
    return {
      amount: '$15.00',
      description: 'Windsurf Pro'
    };
  } else {
    return {
      amount: '$20.00',
      description: 'Cursor Pro'
    };
  }
}

// 主要的Invoice生成函数
export async function generateRandomInvoice(email: string, type: InvoiceType = InvoiceType.WINDSURF, locale: string = 'en_US'): Promise<InvoiceData> {
  const invoiceNumber = generateInvoiceNumber();
  const receiptNumber = generateReceiptNumber();
  const datePaid = generateRandomDate();
  const paymentMethod = generatePaymentMethod();
  const billTo = await generateBillToInfoFromAPI(email, locale);
  const productInfo = generateProductInfo(type);
  const companyInfo = generateCompanyInfo(type);
  const dateRange = generateDateRange(datePaid);

  return {
    type,
    invoiceNumber,
    receiptNumber,
    datePaid,
    paymentMethod,
    billTo,
    amount: productInfo.amount,
    description: productInfo.description,
    dateRange,
    companyInfo
  };
}

// 强制使用本地数据生成器的Invoice生成函数
export async function generateRandomInvoiceLocal(email: string, type: InvoiceType = InvoiceType.WINDSURF, locale: string = 'en_US'): Promise<InvoiceData> {
  const invoiceNumber = generateInvoiceNumber();
  const receiptNumber = generateReceiptNumber();
  const datePaid = generateRandomDate();
  const paymentMethod = generatePaymentMethod();

  // 直接使用本地数据生成器，跳过API调用
  console.log('🏠 强制使用本地数据生成器');
  const billTo = generateLocalBillToInfo(locale);

  // 使用用户输入的邮箱替换随机生成的邮箱
  billTo.email = email;

  const productInfo = generateProductInfo(type);
  const companyInfo = generateCompanyInfo(type);
  const dateRange = generateDateRange(datePaid);

  return {
    type,
    invoiceNumber,
    receiptNumber,
    datePaid,
    paymentMethod,
    billTo,
    amount: productInfo.amount,
    description: productInfo.description,
    dateRange,
    companyInfo
  };
}
