// 完整的 Locale 配置文件
// 支持所有 75 个地区的完整本地化数据

export interface LocaleInfo {
  code: string;
  nameZh: string;
  nameEn: string;
  region: string;
  country: string;
  states: string[];
  firstNames: string[];
  lastNames: string[];
}

export interface RegionGroup {
  name: string;
  emoji: string;
  locales: string[];
}

// 地区分组配置
export const REGION_GROUPS: RegionGroup[] = [
  {
    name: '亚洲 (Asia)',
    emoji: '🌏',
    locales: [
      'zh_CN', 'zh_TW', 'ja_JP', 'ko_KR', 'en_HK', 'en_SG', 'ms_MY', 'th_TH', 
      'vi_VN', 'en_PH', 'id_ID', 'en_IN', 'bn_BD', 'ne_NP', 'fa_IR', 'he_IL', 
      'tr_TR', 'kk_KZ', 'mn_MN', 'hy_AM', 'ka_GE'
    ]
  },
  {
    name: '欧洲 (Europe)',
    emoji: '🌍',
    locales: [
      'en_GB', 'de_DE', 'fr_FR', 'it_IT', 'es_ES', 'ru_RU', 'pl_PL', 'nl_NL', 
      'sv_SE', 'nb_NO', 'da_DK', 'fi_FI', 'is_IS', 'at_AT', 'de_AT', 'de_CH', 
      'fr_CH', 'it_CH', 'fr_BE', 'nl_BE', 'pt_PT', 'el_GR', 'el_CY', 'cs_CZ', 
      'sk_SK', 'hu_HU', 'ro_RO', 'ro_MD', 'bg_BG', 'hr_HR', 'sl_SI', 'sr_RS', 
      'sr_Latn_RS', 'sr_Cyrl_RS', 'me_ME', 'lt_LT', 'lv_LV', 'et_EE', 'uk_UA'
    ]
  },
  {
    name: '北美洲 (North America)',
    emoji: '🌎',
    locales: ['en_US', 'en_CA', 'fr_CA']
  },
  {
    name: '南美洲 (South America)',
    emoji: '🌎',
    locales: ['pt_BR', 'es_AR', 'es_PE', 'es_VE']
  },
  {
    name: '非洲 (Africa)',
    emoji: '🌍',
    locales: ['ar_EG', 'en_ZA', 'en_NG', 'en_UG']
  },
  {
    name: '大洋洲 (Oceania)',
    emoji: '🌏',
    locales: ['en_AU', 'en_NZ']
  },
  {
    name: '中东 (Middle East)',
    emoji: '🕌',
    locales: ['ar_JO', 'ar_SA']
  }
];

// 完整的 Locale 数据配置
export const LOCALE_CONFIG: Record<string, LocaleInfo> = {
  // === 亚洲 ===
  'zh_CN': {
    code: 'zh_CN',
    nameZh: '中国',
    nameEn: 'China',
    region: 'Asia',
    country: 'China',
    states: ['北京市', '上海市', '广东省', '浙江省', '江苏省', '山东省', '河南省', '四川省', '湖北省', '湖南省'],
    firstNames: ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '娟', '涛', '明', '超', '秀英', '霞', '平'],
    lastNames: ['王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗']
  },
  'zh_TW': {
    code: 'zh_TW',
    nameZh: '台湾',
    nameEn: 'Taiwan',
    region: 'Asia',
    country: 'Taiwan',
    states: ['台北市', '新北市', '桃园市', '台中市', '台南市', '高雄市', '新竹县', '苗栗县', '彰化县', '南投县'],
    firstNames: ['志明', '春嬌', '美麗', '建國', '淑芬', '雅婷', '俊傑', '怡君', '宗翰', '佩君', '家豪', '雅雯', '承翰', '怡萱', '宗憲', '佳穎'],
    lastNames: ['陳', '林', '黃', '張', '李', '王', '吳', '劉', '蔡', '楊', '許', '鄭', '謝', '洪', '郭', '邱', '曾', '廖', '賴', '徐']
  },
  'ja_JP': {
    code: 'ja_JP',
    nameZh: '日本',
    nameEn: 'Japan',
    region: 'Asia',
    country: 'Japan',
    states: ['東京都', '大阪府', '神奈川県', '愛知県', '埼玉県', '千葉県', '兵庫県', '北海道', '福岡県', '静岡県'],
    firstNames: ['太郎', '花子', '一郎', '美咲', '翔太', '結衣', '大輔', '愛', '健太', '美穂', '拓也', '麻衣', '雄太', '恵', '慎一', '由美'],
    lastNames: ['田中', '佐藤', '鈴木', '高橋', '渡辺', '伊藤', '山本', '中村', '小林', '加藤', '吉田', '山田', '佐々木', '山口', '松本', '井上']
  },
  'ko_KR': {
    code: 'ko_KR',
    nameZh: '韩国',
    nameEn: 'South Korea',
    region: 'Asia',
    country: 'South Korea',
    states: ['서울특별시', '부산광역시', '대구광역시', '인천광역시', '광주광역시', '대전광역시', '울산광역시', '경기도', '강원도', '충청북도'],
    firstNames: ['민수', '지영', '현우', '수진', '준호', '미영', '성민', '혜진', '동현', '은지', '재현', '소영', '우진', '예은', '태현', '다은'],
    lastNames: ['김', '이', '박', '최', '정', '강', '조', '윤', '장', '임', '한', '오', '서', '신', '권', '황', '안', '송', '류', '전']
  },
  'en_HK': {
    code: 'en_HK',
    nameZh: '香港',
    nameEn: 'Hong Kong',
    region: 'Asia',
    country: 'Hong Kong',
    states: ['Hong Kong Island', 'Kowloon', 'New Territories', 'Central', 'Wan Chai', 'Causeway Bay', 'Tsim Sha Tsui', 'Mong Kok', 'Sha Tin', 'Tuen Mun'],
    firstNames: ['Wing', 'Mei', 'Ka', 'Wai', 'Ming', 'Ling', 'Chun', 'Yuen', 'Ho', 'Fai', 'Siu', 'Kit', 'Man', 'Yan', 'Lok', 'Sum'],
    lastNames: ['Chan', 'Wong', 'Lee', 'Lau', 'Ng', 'Cheung', 'Tang', 'Leung', 'Lam', 'Tsang', 'Ho', 'Yu', 'Lai', 'Fong', 'Mak', 'Tse']
  },
  'en_SG': {
    code: 'en_SG',
    nameZh: '新加坡',
    nameEn: 'Singapore',
    region: 'Asia',
    country: 'Singapore',
    states: ['Central Region', 'East Region', 'North Region', 'North-East Region', 'West Region', 'Orchard', 'Marina Bay', 'Sentosa', 'Jurong', 'Tampines'],
    firstNames: ['Wei Ming', 'Li Hua', 'Raj', 'Priya', 'Ahmad', 'Siti', 'David', 'Sarah', 'Kumar', 'Meera', 'Hassan', 'Fatima', 'James', 'Michelle', 'Arjun', 'Kavitha'],
    lastNames: ['Tan', 'Lim', 'Lee', 'Ng', 'Ong', 'Teo', 'Goh', 'Chua', 'Koh', 'Sim', 'Low', 'Yeo', 'Chong', 'Ho', 'Seah', 'Wee']
  },
  'ms_MY': {
    code: 'ms_MY',
    nameZh: '马来西亚',
    nameEn: 'Malaysia',
    region: 'Asia',
    country: 'Malaysia',
    states: ['Kuala Lumpur', 'Selangor', 'Johor', 'Penang', 'Perak', 'Kedah', 'Kelantan', 'Terengganu', 'Pahang', 'Negeri Sembilan'],
    firstNames: ['Ahmad', 'Siti', 'Muhammad', 'Nur', 'Ali', 'Fatimah', 'Hassan', 'Aminah', 'Ibrahim', 'Khadijah', 'Omar', 'Zainab', 'Ismail', 'Maryam', 'Yusof', 'Aishah'],
    lastNames: ['Abdullah', 'Rahman', 'Ahmad', 'Ali', 'Hassan', 'Ibrahim', 'Ismail', 'Omar', 'Yusof', 'Mohamed', 'Mahmud', 'Hamid', 'Rashid', 'Karim', 'Salleh', 'Mansor']
  },
  'th_TH': {
    code: 'th_TH',
    nameZh: '泰国',
    nameEn: 'Thailand',
    region: 'Asia',
    country: 'Thailand',
    states: ['Bangkok', 'Chiang Mai', 'Phuket', 'Pattaya', 'Krabi', 'Koh Samui', 'Ayutthaya', 'Sukhothai', 'Chiang Rai', 'Hua Hin'],
    firstNames: ['สมชาย', 'สมหญิง', 'วิชัย', 'วันทนา', 'สุรชัย', 'สุมาลี', 'ประยุทธ', 'ประภา', 'วิทยา', 'วิไล', 'สมศักดิ์', 'สมใจ', 'ชาญ', 'ชนิดา', 'ธนา', 'ธิดา'],
    lastNames: ['จันทร์', 'แสง', 'ทอง', 'เงิน', 'แก้ว', 'ใส', 'สว่าง', 'ดี', 'งาม', 'สุข', 'รุ่ง', 'เจริญ', 'มั่น', 'คง', 'ยั่ง', 'ยืน']
  },
  'vi_VN': {
    code: 'vi_VN',
    nameZh: '越南',
    nameEn: 'Vietnam',
    region: 'Asia',
    country: 'Vietnam',
    states: ['Ho Chi Minh City', 'Hanoi', 'Da Nang', 'Hai Phong', 'Can Tho', 'Bien Hoa', 'Hue', 'Nha Trang', 'Buon Ma Thuot', 'Quy Nhon'],
    firstNames: ['Minh', 'Linh', 'Hùng', 'Hương', 'Tuấn', 'Thảo', 'Dũng', 'Dung', 'Hải', 'Hạnh', 'Long', 'Lan', 'Nam', 'Nga', 'Phong', 'Phương'],
    lastNames: ['Nguyễn', 'Trần', 'Lê', 'Phạm', 'Hoàng', 'Huỳnh', 'Phan', 'Vũ', 'Võ', 'Đặng', 'Bùi', 'Đỗ', 'Hồ', 'Ngô', 'Dương', 'Lý']
  },
  'en_PH': {
    code: 'en_PH',
    nameZh: '菲律宾',
    nameEn: 'Philippines',
    region: 'Asia',
    country: 'Philippines',
    states: ['Metro Manila', 'Cebu', 'Davao', 'Quezon City', 'Makati', 'Taguig', 'Pasig', 'Antipolo', 'Cagayan de Oro', 'Zamboanga'],
    firstNames: ['Jose', 'Maria', 'Juan', 'Ana', 'Antonio', 'Rosa', 'Francisco', 'Teresa', 'Pedro', 'Carmen', 'Manuel', 'Luz', 'Ricardo', 'Esperanza', 'Roberto', 'Lourdes'],
    lastNames: ['Santos', 'Reyes', 'Cruz', 'Bautista', 'Ocampo', 'Garcia', 'Mendoza', 'Torres', 'Tomas', 'Andres', 'Marquez', 'Castillo', 'Aquino', 'Flores', 'Villanueva', 'Ramos']
  },
  'id_ID': {
    code: 'id_ID',
    nameZh: '印度尼西亚',
    nameEn: 'Indonesia',
    region: 'Asia',
    country: 'Indonesia',
    states: ['Jakarta', 'Surabaya', 'Bandung', 'Bekasi', 'Medan', 'Tangerang', 'Depok', 'Semarang', 'Palembang', 'Makassar'],
    firstNames: ['Budi', 'Sari', 'Ahmad', 'Dewi', 'Andi', 'Rina', 'Dedi', 'Maya', 'Rudi', 'Indra', 'Lina', 'Agus', 'Fitri', 'Hendra', 'Wati', 'Joko'],
    lastNames: ['Santoso', 'Wijaya', 'Kurniawan', 'Sari', 'Pratama', 'Wibowo', 'Setiawan', 'Lestari', 'Handoko', 'Susanto', 'Permana', 'Hidayat', 'Saputra', 'Maharani', 'Gunawan', 'Purnama']
  },
  'en_IN': {
    code: 'en_IN',
    nameZh: '印度',
    nameEn: 'India',
    region: 'Asia',
    country: 'India',
    states: ['Mumbai', 'Delhi', 'Bangalore', 'Hyderabad', 'Chennai', 'Kolkata', 'Pune', 'Ahmedabad', 'Jaipur', 'Surat'],
    firstNames: ['Raj', 'Priya', 'Amit', 'Sunita', 'Vikram', 'Kavita', 'Suresh', 'Meera', 'Ravi', 'Deepa', 'Anil', 'Pooja', 'Manoj', 'Rekha', 'Sanjay', 'Neha'],
    lastNames: ['Sharma', 'Gupta', 'Singh', 'Kumar', 'Verma', 'Agarwal', 'Jain', 'Patel', 'Shah', 'Mehta', 'Reddy', 'Nair', 'Iyer', 'Rao', 'Pillai', 'Menon']
  },
  'bn_BD': {
    code: 'bn_BD',
    nameZh: '孟加拉国',
    nameEn: 'Bangladesh',
    region: 'Asia',
    country: 'Bangladesh',
    states: ['Dhaka', 'Chittagong', 'Sylhet', 'Rajshahi', 'Khulna', 'Barisal', 'Rangpur', 'Mymensingh', 'Comilla', 'Narayanganj'],
    firstNames: ['আহমেদ', 'ফাতেমা', 'মোহাম্মদ', 'রাহেলা', 'আব্দুল', 'সালমা', 'করিম', 'নাসরিন', 'রহিম', 'শাহিনা', 'হাসান', 'রোকেয়া', 'আলী', 'সুমাইয়া', 'ওমর', 'তাহমিনা'],
    lastNames: ['খান', 'আহমেদ', 'রহমান', 'আলী', 'হোসেন', 'ইসলাম', 'হাসান', 'করিম', 'উদ্দিন', 'বেগম', 'চৌধুরী', 'শেখ', 'মিয়া', 'সরকার', 'দাস', 'রায়']
  },
  'ne_NP': {
    code: 'ne_NP',
    nameZh: '尼泊尔',
    nameEn: 'Nepal',
    region: 'Asia',
    country: 'Nepal',
    states: ['Kathmandu', 'Pokhara', 'Lalitpur', 'Bharatpur', 'Biratnagar', 'Birgunj', 'Dharan', 'Butwal', 'Hetauda', 'Nepalgunj'],
    firstNames: ['राम', 'सीता', 'श्याम', 'गीता', 'हरि', 'माया', 'कृष्ण', 'लक्ष्मी', 'गोपाल', 'सरस्वती', 'विष्णु', 'पार्वती', 'शिव', 'दुर्गा', 'ब्रह्म', 'सुन्दरी'],
    lastNames: ['शर्मा', 'गुरुङ', 'तामाङ', 'राई', 'लिम्बु', 'मगर', 'थापा', 'श्रेष्ठ', 'अधिकारी', 'पौडेल', 'खत्री', 'बस्नेत', 'पन्त', 'जोशी', 'उपाध्याय', 'भट्टराई']
  },
  'fa_IR': {
    code: 'fa_IR',
    nameZh: '伊朗',
    nameEn: 'Iran',
    region: 'Asia',
    country: 'Iran',
    states: ['Tehran', 'Mashhad', 'Isfahan', 'Karaj', 'Shiraz', 'Tabriz', 'Qom', 'Ahvaz', 'Kermanshah', 'Urmia'],
    firstNames: ['محمد', 'فاطمه', 'علی', 'زهرا', 'حسن', 'مریم', 'احمد', 'آسیه', 'حسین', 'خدیجه', 'رضا', 'عایشه', 'مصطفی', 'سکینه', 'عباس', 'زینب'],
    lastNames: ['احمدی', 'محمدی', 'حسینی', 'رضایی', 'علیزاده', 'محمدزاده', 'حسنی', 'کریمی', 'جعفری', 'مرادی', 'یوسفی', 'صادقی', 'نوری', 'کاظمی', 'موسوی', 'هاشمی']
  },
  'he_IL': {
    code: 'he_IL',
    nameZh: '以色列',
    nameEn: 'Israel',
    region: 'Asia',
    country: 'Israel',
    states: ['Jerusalem', 'Tel Aviv', 'Haifa', 'Rishon LeZion', 'Petah Tikva', 'Ashdod', 'Netanya', 'Beer Sheva', 'Holon', 'Bnei Brak'],
    firstNames: ['דוד', 'שרה', 'משה', 'רחל', 'יוסף', 'לאה', 'אברהם', 'רבקה', 'יעקב', 'מרים', 'אהרן', 'דינה', 'שמואל', 'אסתר', 'דניאל', 'רות'],
    lastNames: ['כהן', 'לוי', 'מזרחי', 'פרידמן', 'דהן', 'אברהם', 'דוד', 'יוסף', 'חיים', 'בן דוד', 'שמיר', 'גולן', 'בר', 'רוזן', 'שפירא', 'גרוס']
  },
  'tr_TR': {
    code: 'tr_TR',
    nameZh: '土耳其',
    nameEn: 'Turkey',
    region: 'Asia',
    country: 'Turkey',
    states: ['Istanbul', 'Ankara', 'Izmir', 'Bursa', 'Adana', 'Gaziantep', 'Konya', 'Antalya', 'Kayseri', 'Mersin'],
    firstNames: ['Mehmet', 'Ayşe', 'Mustafa', 'Fatma', 'Ahmet', 'Emine', 'Ali', 'Hatice', 'Hüseyin', 'Zeynep', 'Hasan', 'Elif', 'İbrahim', 'Meryem', 'Ömer', 'Aynur'],
    lastNames: ['Yılmaz', 'Kaya', 'Demir', 'Şahin', 'Çelik', 'Şen', 'Özkan', 'Arslan', 'Doğan', 'Kilic', 'Aslan', 'Çetin', 'Kara', 'Koç', 'Kurt', 'Özdemir']
  },
  'kk_KZ': {
    code: 'kk_KZ',
    nameZh: '哈萨克斯坦',
    nameEn: 'Kazakhstan',
    region: 'Asia',
    country: 'Kazakhstan',
    states: ['Almaty', 'Nur-Sultan', 'Shymkent', 'Aktobe', 'Taraz', 'Pavlodar', 'Ust-Kamenogorsk', 'Semey', 'Atyrau', 'Kostanay'],
    firstNames: ['Айдар', 'Айгүл', 'Асылбек', 'Гүлнар', 'Ерлан', 'Жанар', 'Қайрат', 'Күлпаш', 'Мұрат', 'Назгүл', 'Нұрлан', 'Сауле', 'Серік', 'Тоқтар', 'Ұлан', 'Фарида'],
    lastNames: ['Әбдіқадыров', 'Байжанов', 'Досмұхамедов', 'Жақсылықов', 'Қасымов', 'Мұхамедов', 'Нұрғалиев', 'Оразбаев', 'Сейітов', 'Тоқтаров', 'Үмбетов', 'Хасенов', 'Шәріпов', 'Ысқақов', 'Әлиев', 'Өтебаев']
  },
  'mn_MN': {
    code: 'mn_MN',
    nameZh: '蒙古',
    nameEn: 'Mongolia',
    region: 'Asia',
    country: 'Mongolia',
    states: ['Ulaanbaatar', 'Erdenet', 'Darkhan', 'Choibalsan', 'Murun', 'Bayankhongor', 'Mandalgovi', 'Uliastai', 'Khovd', 'Olgii'],
    firstNames: ['Батбаяр', 'Оюунчимэг', 'Болд', 'Сарангэрэл', 'Төмөр', 'Цэцэгмаа', 'Энхбаяр', 'Алтанцэцэг', 'Мөнх', 'Наранцэцэг', 'Пүрэв', 'Одонтуяа', 'Ганбат', 'Мөнхзул', 'Батсайхан', 'Цэрэндолгор'],
    lastNames: ['Батбаяр', 'Доржийн', 'Энхбаяр', 'Цэндийн', 'Батмөнх', 'Пүрэвийн', 'Болдбаатар', 'Цэрэндаш', 'Мөнхбат', 'Алтангэрэл', 'Батсүх', 'Цэвээн', 'Энхтөр', 'Баттөмөр', 'Цагаан', 'Мөнхсайхан']
  },
  'hy_AM': {
    code: 'hy_AM',
    nameZh: '亚美尼亚',
    nameEn: 'Armenia',
    region: 'Asia',
    country: 'Armenia',
    states: ['Yerevan', 'Gyumri', 'Vanadzor', 'Vagharshapat', 'Hrazdan', 'Abovyan', 'Kapan', 'Armavir', 'Gavar', 'Artashat'],
    firstNames: ['Արամ', 'Անահիտ', 'Դավիթ', 'Գայանե', 'Հայկ', 'Լուսինե', 'Նարեկ', 'Մարիամ', 'Տիգրան', 'Սիրանուշ', 'Վահան', 'Արմինե', 'Գագիկ', 'Նվարդ', 'Ռաֆայել', 'Զարուհի'],
    lastNames: ['Հակոբյան', 'Պետրոսյան', 'Գրիգորյան', 'Ավետիսյան', 'Մարտիրոսյան', 'Ղազարյան', 'Վարդանյան', 'Սարգսյան', 'Ալեքսանյան', 'Դանիելյան', 'Մանուկյան', 'Ստեփանյան', 'Բաղդասարյան', 'Խաչատրյան', 'Ներսիսյան', 'Մկրտչյան']
  },
  'ka_GE': {
    code: 'ka_GE',
    nameZh: '格鲁吉亚',
    nameEn: 'Georgia',
    region: 'Asia',
    country: 'Georgia',
    states: ['Tbilisi', 'Kutaisi', 'Batumi', 'Rustavi', 'Zugdidi', 'Gori', 'Poti', 'Kobuleti', 'Khashuri', 'Samtredia'],
    firstNames: ['გიორგი', 'ნინო', 'დავით', 'მარიამ', 'ლევან', 'ნანა', 'ირაკლი', 'თამარ', 'ზურაბ', 'ლელა', 'გიორგი', 'ნათია', 'ვახტანგ', 'მაია', 'ალექსანდრე', 'ეკა'],
    lastNames: ['მამედოვი', 'ბერიძე', 'კვარაცხელია', 'ლომიძე', 'ღლონტი', 'ჯაფარიძე', 'ხარაიშვილი', 'ცინცაძე', 'მეგრელიშვილი', 'ღუდუშაური', 'ბოლქვაძე', 'ჩხეიძე', 'ღამბაშიძე', 'ლობჟანიძე', 'ღვინიაშვილი', 'ჯანელიძე']
  },

  // === 欧洲 ===
  'en_GB': {
    code: 'en_GB',
    nameZh: '英国',
    nameEn: 'United Kingdom',
    region: 'Europe',
    country: 'United Kingdom',
    states: ['London', 'Birmingham', 'Manchester', 'Glasgow', 'Liverpool', 'Leeds', 'Sheffield', 'Edinburgh', 'Bristol', 'Cardiff'],
    firstNames: ['James', 'Mary', 'John', 'Patricia', 'Robert', 'Jennifer', 'Michael', 'Linda', 'William', 'Elizabeth', 'David', 'Barbara', 'Richard', 'Susan', 'Joseph', 'Jessica'],
    lastNames: ['Smith', 'Jones', 'Taylor', 'Brown', 'Williams', 'Wilson', 'Johnson', 'Davies', 'Robinson', 'Wright', 'Thompson', 'Evans', 'Walker', 'White', 'Roberts', 'Green']
  },
  'de_DE': {
    code: 'de_DE',
    nameZh: '德国',
    nameEn: 'Germany',
    region: 'Europe',
    country: 'Germany',
    states: ['Berlin', 'Hamburg', 'Munich', 'Cologne', 'Frankfurt', 'Stuttgart', 'Düsseldorf', 'Dortmund', 'Essen', 'Leipzig'],
    firstNames: ['Hans', 'Anna', 'Peter', 'Maria', 'Wolfgang', 'Elisabeth', 'Klaus', 'Ursula', 'Jürgen', 'Ingrid', 'Dieter', 'Helga', 'Horst', 'Gisela', 'Uwe', 'Monika'],
    lastNames: ['Müller', 'Schmidt', 'Schneider', 'Fischer', 'Weber', 'Meyer', 'Wagner', 'Becker', 'Schulz', 'Hoffmann', 'Schäfer', 'Koch', 'Bauer', 'Richter', 'Klein', 'Wolf']
  },
  'fr_FR': {
    code: 'fr_FR',
    nameZh: '法国',
    nameEn: 'France',
    region: 'Europe',
    country: 'France',
    states: ['Paris', 'Marseille', 'Lyon', 'Toulouse', 'Nice', 'Nantes', 'Strasbourg', 'Montpellier', 'Bordeaux', 'Lille'],
    firstNames: ['Jean', 'Marie', 'Pierre', 'Françoise', 'Michel', 'Monique', 'Alain', 'Catherine', 'Philippe', 'Sylvie', 'Bernard', 'Martine', 'Christian', 'Christine', 'Daniel', 'Nathalie'],
    lastNames: ['Martin', 'Bernard', 'Thomas', 'Petit', 'Robert', 'Richard', 'Durand', 'Dubois', 'Moreau', 'Laurent', 'Simon', 'Michel', 'Lefebvre', 'Leroy', 'Roux', 'David']
  },
  'it_IT': {
    code: 'it_IT',
    nameZh: '意大利',
    nameEn: 'Italy',
    region: 'Europe',
    country: 'Italy',
    states: ['Rome', 'Milan', 'Naples', 'Turin', 'Palermo', 'Genoa', 'Bologna', 'Florence', 'Bari', 'Catania'],
    firstNames: ['Giuseppe', 'Maria', 'Antonio', 'Anna', 'Francesco', 'Giuseppina', 'Giovanni', 'Rosa', 'Luigi', 'Angela', 'Vincenzo', 'Giovanna', 'Michele', 'Teresa', 'Salvatore', 'Lucia'],
    lastNames: ['Rossi', 'Russo', 'Ferrari', 'Esposito', 'Bianchi', 'Romano', 'Colombo', 'Ricci', 'Marino', 'Greco', 'Bruno', 'Gallo', 'Conti', 'De Luca', 'Mancini', 'Costa']
  },
  'es_ES': {
    code: 'es_ES',
    nameZh: '西班牙',
    nameEn: 'Spain',
    region: 'Europe',
    country: 'Spain',
    states: ['Madrid', 'Barcelona', 'Valencia', 'Seville', 'Zaragoza', 'Málaga', 'Murcia', 'Palma', 'Las Palmas', 'Bilbao'],
    firstNames: ['Antonio', 'María', 'José', 'Carmen', 'Francisco', 'Josefa', 'Juan', 'Isabel', 'Manuel', 'Ana', 'David', 'Dolores', 'José Antonio', 'Pilar', 'Jesús', 'Teresa'],
    lastNames: ['García', 'Rodríguez', 'González', 'Fernández', 'López', 'Martínez', 'Sánchez', 'Pérez', 'Gómez', 'Martín', 'Jiménez', 'Ruiz', 'Hernández', 'Díaz', 'Moreno', 'Muñoz']
  },
  'ru_RU': {
    code: 'ru_RU',
    nameZh: '俄罗斯',
    nameEn: 'Russia',
    region: 'Europe',
    country: 'Russia',
    states: ['Moscow', 'Saint Petersburg', 'Novosibirsk', 'Yekaterinburg', 'Nizhny Novgorod', 'Kazan', 'Chelyabinsk', 'Omsk', 'Samara', 'Rostov-on-Don'],
    firstNames: ['Александр', 'Елена', 'Сергей', 'Татьяна', 'Андрей', 'Ольга', 'Дмитрий', 'Наталья', 'Алексей', 'Ирина', 'Владимир', 'Светлана', 'Евгений', 'Людмила', 'Николай', 'Галина'],
    lastNames: ['Иванов', 'Смирнов', 'Кузнецов', 'Попов', 'Васильев', 'Петров', 'Соколов', 'Михайлов', 'Новиков', 'Федоров', 'Морозов', 'Волков', 'Алексеев', 'Лебедев', 'Семенов', 'Егоров']
  },
  'pl_PL': {
    code: 'pl_PL',
    nameZh: '波兰',
    nameEn: 'Poland',
    region: 'Europe',
    country: 'Poland',
    states: ['Warsaw', 'Kraków', 'Łódź', 'Wrocław', 'Poznań', 'Gdańsk', 'Szczecin', 'Bydgoszcz', 'Lublin', 'Katowice'],
    firstNames: ['Jan', 'Anna', 'Andrzej', 'Maria', 'Piotr', 'Katarzyna', 'Krzysztof', 'Małgorzata', 'Stanisław', 'Agnieszka', 'Tomasz', 'Barbara', 'Paweł', 'Ewa', 'Józef', 'Elżbieta'],
    lastNames: ['Nowak', 'Kowalski', 'Wiśniewski', 'Wójcik', 'Kowalczyk', 'Kamiński', 'Lewandowski', 'Zieliński', 'Szymański', 'Woźniak', 'Dąbrowski', 'Kozłowski', 'Jankowski', 'Mazur', 'Kwiatkowski', 'Krawczyk']
  },
  'nl_NL': {
    code: 'nl_NL',
    nameZh: '荷兰',
    nameEn: 'Netherlands',
    region: 'Europe',
    country: 'Netherlands',
    states: ['Amsterdam', 'Rotterdam', 'The Hague', 'Utrecht', 'Eindhoven', 'Tilburg', 'Groningen', 'Almere', 'Breda', 'Nijmegen'],
    firstNames: ['Jan', 'Maria', 'Piet', 'Anna', 'Kees', 'Johanna', 'Henk', 'Elisabeth', 'Gerrit', 'Catharina', 'Johannes', 'Margaretha', 'Cornelis', 'Hendrika', 'Willem', 'Francisca'],
    lastNames: ['de Jong', 'Jansen', 'de Vries', 'van den Berg', 'van Dijk', 'Bakker', 'Janssen', 'Visser', 'Smit', 'Meijer', 'de Boer', 'Mulder', 'de Groot', 'Bos', 'Vos', 'Peters']
  },
  'sv_SE': {
    code: 'sv_SE',
    nameZh: '瑞典',
    nameEn: 'Sweden',
    region: 'Europe',
    country: 'Sweden',
    states: ['Stockholm', 'Gothenburg', 'Malmö', 'Uppsala', 'Västerås', 'Örebro', 'Linköping', 'Helsingborg', 'Jönköping', 'Norrköping'],
    firstNames: ['Lars', 'Anna', 'Nils', 'Maria', 'Erik', 'Elisabeth', 'Karl', 'Margareta', 'Anders', 'Birgitta', 'Per', 'Ingrid', 'Johan', 'Karin', 'Olof', 'Astrid'],
    lastNames: ['Andersson', 'Johansson', 'Karlsson', 'Nilsson', 'Eriksson', 'Larsson', 'Olsson', 'Persson', 'Svensson', 'Gustafsson', 'Pettersson', 'Jonsson', 'Jansson', 'Hansson', 'Bengtsson', 'Jönsson']
  },
  'nb_NO': {
    code: 'nb_NO',
    nameZh: '挪威',
    nameEn: 'Norway',
    region: 'Europe',
    country: 'Norway',
    states: ['Oslo', 'Bergen', 'Stavanger', 'Trondheim', 'Drammen', 'Fredrikstad', 'Kristiansand', 'Sandnes', 'Tromsø', 'Sarpsborg'],
    firstNames: ['Lars', 'Anne', 'Per', 'Inger', 'Ole', 'Kari', 'Jan', 'Marit', 'Kjell', 'Randi', 'Nils', 'Astrid', 'Arne', 'Solveig', 'Svein', 'Liv'],
    lastNames: ['Hansen', 'Johansen', 'Olsen', 'Larsen', 'Andersen', 'Pedersen', 'Nilsen', 'Kristiansen', 'Jensen', 'Karlsen', 'Johnsen', 'Pettersen', 'Eriksen', 'Berg', 'Haugen', 'Hagen']
  },
  'da_DK': {
    code: 'da_DK',
    nameZh: '丹麦',
    nameEn: 'Denmark',
    region: 'Europe',
    country: 'Denmark',
    states: ['Copenhagen', 'Aarhus', 'Odense', 'Aalborg', 'Esbjerg', 'Randers', 'Kolding', 'Horsens', 'Vejle', 'Roskilde'],
    firstNames: ['Lars', 'Anne', 'Per', 'Kirsten', 'Jens', 'Hanne', 'Peter', 'Lene', 'Michael', 'Susanne', 'Henrik', 'Lone', 'Thomas', 'Pia', 'Jan', 'Bente'],
    lastNames: ['Nielsen', 'Jensen', 'Hansen', 'Pedersen', 'Andersen', 'Christensen', 'Larsen', 'Sørensen', 'Rasmussen', 'Jørgensen', 'Petersen', 'Madsen', 'Kristensen', 'Olsen', 'Thomsen', 'Christiansen']
  },
  'fi_FI': {
    code: 'fi_FI',
    nameZh: '芬兰',
    nameEn: 'Finland',
    region: 'Europe',
    country: 'Finland',
    states: ['Helsinki', 'Espoo', 'Tampere', 'Vantaa', 'Oulu', 'Turku', 'Jyväskylä', 'Lahti', 'Kuopio', 'Pori'],
    firstNames: ['Juhani', 'Maria', 'Johannes', 'Helena', 'Olavi', 'Johanna', 'Antero', 'Anneli', 'Tapani', 'Kaarina', 'Kalevi', 'Liisa', 'Mikael', 'Margareta', 'Matti', 'Elisabeth'],
    lastNames: ['Korhonen', 'Virtanen', 'Mäkinen', 'Nieminen', 'Mäkelä', 'Hämäläinen', 'Laine', 'Heikkinen', 'Koskinen', 'Järvinen', 'Lehtonen', 'Lehtinen', 'Saarinen', 'Salminen', 'Heinonen', 'Niemi']
  },
  'is_IS': {
    code: 'is_IS',
    nameZh: '冰岛',
    nameEn: 'Iceland',
    region: 'Europe',
    country: 'Iceland',
    states: ['Reykjavík', 'Kópavogur', 'Hafnarfjörður', 'Akureyri', 'Reykjanesbær', 'Garðabær', 'Mosfellsbær', 'Árborg', 'Akranes', 'Fjarðabyggð'],
    firstNames: ['Jón', 'Guðrún', 'Sigurður', 'Anna', 'Gunnar', 'Kristín', 'Ólafur', 'Margrét', 'Magnús', 'Sigríður', 'Einar', 'Helga', 'Kristján', 'Ragnhildur', 'Þórður', 'Ingibjörg'],
    lastNames: ['Jónsson', 'Sigurðsson', 'Guðmundsson', 'Einarsson', 'Magnússon', 'Ólafsson', 'Kristjánsson', 'Arnarson', 'Jóhannsson', 'Gunnarsson', 'Pétursson', 'Stefánsson', 'Gíslason', 'Þórsson', 'Ragnarsson', 'Helgason']
  },
  'at_AT': {
    code: 'at_AT',
    nameZh: '奥地利',
    nameEn: 'Austria',
    region: 'Europe',
    country: 'Austria',
    states: ['Vienna', 'Graz', 'Linz', 'Salzburg', 'Innsbruck', 'Klagenfurt', 'Villach', 'Wels', 'Sankt Pölten', 'Dornbirn'],
    firstNames: ['Johann', 'Maria', 'Franz', 'Anna', 'Josef', 'Elisabeth', 'Karl', 'Theresia', 'Georg', 'Katharina', 'Michael', 'Margarete', 'Anton', 'Barbara', 'Peter', 'Christine'],
    lastNames: ['Gruber', 'Huber', 'Bauer', 'Wagner', 'Müller', 'Pichler', 'Steiner', 'Moser', 'Mayer', 'Hofer', 'Leitner', 'Berger', 'Fuchs', 'Eder', 'Fischer', 'Schmid']
  },
  'de_AT': {
    code: 'de_AT',
    nameZh: '奥地利德语',
    nameEn: 'Austria German',
    region: 'Europe',
    country: 'Austria',
    states: ['Wien', 'Graz', 'Linz', 'Salzburg', 'Innsbruck', 'Klagenfurt', 'Villach', 'Wels', 'Sankt Pölten', 'Dornbirn'],
    firstNames: ['Johann', 'Maria', 'Franz', 'Anna', 'Josef', 'Elisabeth', 'Karl', 'Theresia', 'Georg', 'Katharina', 'Michael', 'Margarete', 'Anton', 'Barbara', 'Peter', 'Christine'],
    lastNames: ['Gruber', 'Huber', 'Bauer', 'Wagner', 'Müller', 'Pichler', 'Steiner', 'Moser', 'Mayer', 'Hofer', 'Leitner', 'Berger', 'Fuchs', 'Eder', 'Fischer', 'Schmid']
  },

  // === 北美洲 ===
  'en_US': {
    code: 'en_US',
    nameZh: '美国',
    nameEn: 'United States',
    region: 'North America',
    country: 'United States',
    states: ['California', 'Texas', 'Florida', 'New York', 'Pennsylvania', 'Illinois', 'Ohio', 'Georgia', 'North Carolina', 'Michigan'],
    firstNames: ['James', 'Mary', 'John', 'Patricia', 'Robert', 'Jennifer', 'Michael', 'Linda', 'William', 'Elizabeth', 'David', 'Barbara', 'Richard', 'Susan', 'Joseph', 'Jessica'],
    lastNames: ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson', 'Thomas']
  },
  'en_CA': {
    code: 'en_CA',
    nameZh: '加拿大',
    nameEn: 'Canada',
    region: 'North America',
    country: 'Canada',
    states: ['Ontario', 'Quebec', 'British Columbia', 'Alberta', 'Manitoba', 'Saskatchewan', 'Nova Scotia', 'New Brunswick', 'Newfoundland and Labrador', 'Prince Edward Island'],
    firstNames: ['James', 'Mary', 'John', 'Patricia', 'Robert', 'Jennifer', 'Michael', 'Linda', 'William', 'Elizabeth', 'David', 'Barbara', 'Richard', 'Susan', 'Joseph', 'Jessica'],
    lastNames: ['Smith', 'Brown', 'Tremblay', 'Martin', 'Roy', 'Wilson', 'MacDonald', 'Johnson', 'Thompson', 'Anderson', 'Clark', 'Scott', 'Campbell', 'Stewart', 'Roberts', 'Cook']
  },
  'fr_CA': {
    code: 'fr_CA',
    nameZh: '加拿大法语',
    nameEn: 'Canada French',
    region: 'North America',
    country: 'Canada',
    states: ['Québec', 'Ontario', 'Nouveau-Brunswick', 'Manitoba', 'Colombie-Britannique', 'Alberta', 'Saskatchewan', 'Nouvelle-Écosse', 'Terre-Neuve-et-Labrador', 'Île-du-Prince-Édouard'],
    firstNames: ['Jean', 'Marie', 'Pierre', 'Françoise', 'Michel', 'Monique', 'Alain', 'Catherine', 'Philippe', 'Sylvie', 'Bernard', 'Martine', 'Christian', 'Christine', 'Daniel', 'Nathalie'],
    lastNames: ['Tremblay', 'Gagnon', 'Roy', 'Côté', 'Bouchard', 'Gauthier', 'Morin', 'Lavoie', 'Fortin', 'Gagné', 'Ouellet', 'Pelletier', 'Bélanger', 'Lévesque', 'Bergeron', 'Leblanc']
  },

  // === 南美洲 ===
  'pt_BR': {
    code: 'pt_BR',
    nameZh: '巴西',
    nameEn: 'Brazil',
    region: 'South America',
    country: 'Brazil',
    states: ['São Paulo', 'Rio de Janeiro', 'Belo Horizonte', 'Brasília', 'Salvador', 'Fortaleza', 'Curitiba', 'Manaus', 'Recife', 'Porto Alegre'],
    firstNames: ['José', 'Maria', 'Antonio', 'Ana', 'Francisco', 'Francisca', 'João', 'Antônia', 'Carlos', 'Luiza', 'Paulo', 'Luzia', 'Pedro', 'Adriana', 'Lucas', 'Juliana'],
    lastNames: ['Silva', 'Santos', 'Oliveira', 'Souza', 'Rodrigues', 'Ferreira', 'Alves', 'Pereira', 'Lima', 'Gomes', 'Ribeiro', 'Carvalho', 'Ramos', 'Almeida', 'Lopes', 'Soares']
  },
  'es_AR': {
    code: 'es_AR',
    nameZh: '阿根廷',
    nameEn: 'Argentina',
    region: 'South America',
    country: 'Argentina',
    states: ['Buenos Aires', 'Córdoba', 'Rosario', 'Mendoza', 'La Plata', 'San Miguel de Tucumán', 'Mar del Plata', 'Salta', 'Santa Fe', 'San Juan'],
    firstNames: ['Juan', 'María', 'José', 'Ana', 'Carlos', 'Patricia', 'Luis', 'Marta', 'Jorge', 'Carmen', 'Antonio', 'Laura', 'Francisco', 'Rosa', 'Miguel', 'Teresa'],
    lastNames: ['González', 'Rodríguez', 'Gómez', 'Fernández', 'López', 'Díaz', 'Martínez', 'Pérez', 'García', 'Martín', 'Sánchez', 'Romero', 'Sosa', 'Vargas', 'Castro', 'Ruiz']
  },

  // === 非洲 ===
  'ar_EG': {
    code: 'ar_EG',
    nameZh: '埃及',
    nameEn: 'Egypt',
    region: 'Africa',
    country: 'Egypt',
    states: ['Cairo', 'Alexandria', 'Giza', 'Shubra El-Kheima', 'Port Said', 'Suez', 'Luxor', 'Mansoura', 'El-Mahalla El-Kubra', 'Tanta'],
    firstNames: ['محمد', 'فاطمة', 'أحمد', 'عائشة', 'علي', 'خديجة', 'حسن', 'زينب', 'عبدالله', 'مريم', 'إبراهيم', 'نور', 'عمر', 'سارة', 'يوسف', 'ليلى'],
    lastNames: ['محمد', 'أحمد', 'علي', 'حسن', 'إبراهيم', 'عبدالله', 'محمود', 'عبدالرحمن', 'خالد', 'عمر', 'يوسف', 'السيد', 'عبدالعزيز', 'مصطفى', 'طه', 'عبدالحميد']
  },
  'en_ZA': {
    code: 'en_ZA',
    nameZh: '南非',
    nameEn: 'South Africa',
    region: 'Africa',
    country: 'South Africa',
    states: ['Johannesburg', 'Cape Town', 'Durban', 'Pretoria', 'Port Elizabeth', 'Bloemfontein', 'East London', 'Pietermaritzburg', 'Nelspruit', 'Kimberley'],
    firstNames: ['John', 'Mary', 'David', 'Susan', 'Michael', 'Elizabeth', 'James', 'Patricia', 'Robert', 'Jennifer', 'William', 'Linda', 'Richard', 'Barbara', 'Joseph', 'Jessica'],
    lastNames: ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Miller', 'Davis', 'Garcia', 'Rodriguez', 'Wilson', 'Martinez', 'Anderson', 'Taylor', 'Thomas', 'Hernandez', 'Moore']
  },

  // === 大洋洲 ===
  'en_AU': {
    code: 'en_AU',
    nameZh: '澳大利亚',
    nameEn: 'Australia',
    region: 'Oceania',
    country: 'Australia',
    states: ['Sydney', 'Melbourne', 'Brisbane', 'Perth', 'Adelaide', 'Gold Coast', 'Newcastle', 'Canberra', 'Sunshine Coast', 'Wollongong'],
    firstNames: ['William', 'Jessica', 'Jack', 'Emily', 'Oliver', 'Charlotte', 'James', 'Olivia', 'Benjamin', 'Ava', 'Lucas', 'Amelia', 'Henry', 'Mia', 'Alexander', 'Isabella'],
    lastNames: ['Smith', 'Jones', 'Williams', 'Brown', 'Wilson', 'Taylor', 'Johnson', 'White', 'Martin', 'Anderson', 'Thompson', 'Nguyen', 'Thomas', 'Walker', 'Harris', 'Lee']
  },
  'en_NZ': {
    code: 'en_NZ',
    nameZh: '新西兰',
    nameEn: 'New Zealand',
    region: 'Oceania',
    country: 'New Zealand',
    states: ['Auckland', 'Wellington', 'Christchurch', 'Hamilton', 'Tauranga', 'Napier-Hastings', 'Dunedin', 'Palmerston North', 'Nelson', 'Rotorua'],
    firstNames: ['James', 'Emma', 'William', 'Charlotte', 'Oliver', 'Sophie', 'Jack', 'Emily', 'Noah', 'Amelia', 'Lucas', 'Ava', 'Mason', 'Isabella', 'Ethan', 'Mia'],
    lastNames: ['Smith', 'Jones', 'Williams', 'Brown', 'Wilson', 'Taylor', 'Johnson', 'White', 'Martin', 'Anderson', 'Thompson', 'Clark', 'Lewis', 'Walker', 'Hall', 'Young']
  },

  // === 中东 ===
  'ar_JO': {
    code: 'ar_JO',
    nameZh: '约旦',
    nameEn: 'Jordan',
    region: 'Middle East',
    country: 'Jordan',
    states: ['Amman', 'Zarqa', 'Irbid', 'Russeifa', 'Wadi as-Sir', 'Ajloun', 'Aqaba', 'Madaba', 'Salt', 'Mafraq'],
    firstNames: ['محمد', 'فاطمة', 'أحمد', 'عائشة', 'علي', 'خديجة', 'حسن', 'زينب', 'عبدالله', 'مريم', 'إبراهيم', 'نور', 'عمر', 'سارة', 'يوسف', 'ليلى'],
    lastNames: ['العبدالله', 'المحمد', 'الأحمد', 'الخطيب', 'القاسم', 'الزعبي', 'الطراونة', 'العمري', 'الشريف', 'الحموري', 'النسور', 'الخوالدة', 'البطاينة', 'الرواشدة', 'الزيود', 'المجالي']
  },
  'ar_SA': {
    code: 'ar_SA',
    nameZh: '沙特阿拉伯',
    nameEn: 'Saudi Arabia',
    region: 'Middle East',
    country: 'Saudi Arabia',
    states: ['Riyadh', 'Jeddah', 'Mecca', 'Medina', 'Dammam', 'Khobar', 'Tabuk', 'Buraidah', 'Khamis Mushait', 'Hofuf'],
    firstNames: ['محمد', 'فاطمة', 'عبدالله', 'نورا', 'أحمد', 'سارة', 'علي', 'مريم', 'عبدالرحمن', 'عائشة', 'خالد', 'هند', 'عمر', 'زينب', 'سعد', 'أمل'],
    lastNames: ['آل سعود', 'العتيبي', 'الغامدي', 'القحطاني', 'الحربي', 'المطيري', 'الدوسري', 'الشهري', 'الزهراني', 'العنزي', 'الشمري', 'الخالدي', 'العسيري', 'الجهني', 'الرشيد', 'البقمي']
  }
};

// 辅助函数：根据 locale 代码获取配置信息
export function getLocaleInfo(localeCode: string): LocaleInfo | null {
  return LOCALE_CONFIG[localeCode] || null;
}

// 辅助函数：根据地区名称获取该地区的所有 locale
export function getLocalesByRegion(regionName: string): LocaleInfo[] {
  const region = REGION_GROUPS.find(r => r.name === regionName);
  if (!region) return [];

  return region.locales.map(code => LOCALE_CONFIG[code]).filter(Boolean);
}

// 辅助函数：获取所有可用的 locale 代码
export function getAllLocaleCodes(): string[] {
  return Object.keys(LOCALE_CONFIG);
}

// 辅助函数：根据国家名称搜索 locale
export function getLocalesByCountry(countryName: string): LocaleInfo[] {
  return Object.values(LOCALE_CONFIG).filter(locale =>
    locale.country.toLowerCase().includes(countryName.toLowerCase()) ||
    locale.nameEn.toLowerCase().includes(countryName.toLowerCase()) ||
    locale.nameZh.includes(countryName)
  );
}
